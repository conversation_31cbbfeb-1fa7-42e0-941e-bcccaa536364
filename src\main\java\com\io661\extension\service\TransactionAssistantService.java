package com.io661.extension.service;

import com.io661.extension.model.Steam.*;

import java.util.List;

public interface TransactionAssistantService {
    /**
     * 获取库存列表
     */
    List<SteamInventoryRes> getInventoryList(Integer type, String steamId, Integer limit, Integer subType, boolean onSell, Integer sort, Integer page, String search);

    /**
     * 上架物品到市场
     *
     * @param request 上架请求
     * @return 上架响应
     */
    MarketListingResponse listItemsOnMarket(MarketListingRequest request);

    /**
     * 变更物品上下架状态
     *
     * @param request 上下架请求
     * @return 上下架响应
     */
    ChangeOnSellStatusRes changeOnSellStatus(ChangeOnSellStatusReq request);
}
