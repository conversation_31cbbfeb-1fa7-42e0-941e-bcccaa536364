package com.io661.extension.util;

import javax.net.ssl.*;
import java.io.FileInputStream;
import java.io.InputStream;
import java.security.KeyStore;
import java.security.cert.Certificate;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 全局SSL证书管理器
 * 支持多域名和动态HTTPS URL的SSL配置
 * 用于配置自定义SSL证书以避免HTTPS握手失败
 */
public class SSLCertificateManager {

    private static SSLContext sslContext;
    private static boolean initialized = false;
    private static boolean globalSSLEnabled = false;

    // 支持的域名列表（可以动态添加）
    private static final Set<String> trustedDomains = new HashSet<>(Arrays.asList(
        "io661.com",
        "open.io661.com",
        "s3.io661.com",
        "api.io661.com",
        "cdn.io661.com"
        // 可以根据需要添加更多域名
    ));
    
    /**
     * 启用全局SSL配置
     * 这将为所有HTTPS连接设置默认的SSL配置
     */
    public static synchronized void enableGlobalSSL() {
        if (!globalSSLEnabled) {
            initializeSSLContext();

            // 设置全局默认的SSLSocketFactory和HostnameVerifier
            HttpsURLConnection.setDefaultSSLSocketFactory(getSSLSocketFactory());
            HttpsURLConnection.setDefaultHostnameVerifier(getHostnameVerifier());

            globalSSLEnabled = true;
            System.out.println("已启用全局SSL配置 - 所有HTTPS连接将使用自定义SSL设置");
        }
    }

    /**
     * 禁用全局SSL配置，恢复系统默认设置
     */
    public static synchronized void disableGlobalSSL() {
        if (globalSSLEnabled) {
            // 恢复系统默认设置
            HttpsURLConnection.setDefaultSSLSocketFactory((SSLSocketFactory) SSLSocketFactory.getDefault());
            HttpsURLConnection.setDefaultHostnameVerifier(HttpsURLConnection.getDefaultHostnameVerifier());

            globalSSLEnabled = false;
            System.out.println("已禁用全局SSL配置 - 恢复系统默认SSL设置");
        }
    }

    /**
     * 添加信任的域名
     */
    public static void addTrustedDomain(String domain) {
        trustedDomains.add(domain);
        System.out.println("已添加信任域名: " + domain);
    }

    /**
     * 批量添加信任的域名
     */
    public static void addTrustedDomains(String... domains) {
        for (String domain : domains) {
            trustedDomains.add(domain);
        }
        System.out.println("已添加信任域名: " + Arrays.toString(domains));
    }

    /**
     * 初始化SSL上下文，加载自定义证书
     */
    public static synchronized void initializeSSLContext() {
        if (initialized) {
            return;
        }

        try {
            // 直接使用信任所有证书的模式，确保HTTPS连接能够工作
            createTrustAllSSLContext();
            System.out.println("SSL证书配置成功 - 已启用信任所有证书模式（推荐用于开发环境）");
        } catch (Exception e) {
            System.err.println("SSL证书配置失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 加载自定义证书
     * @return 是否成功加载证书
     */
    private static boolean loadCustomCertificates() {
        try {
        // 创建证书工厂
        CertificateFactory cf = CertificateFactory.getInstance("X.509");

        // 尝试加载不同的证书文件
        InputStream certInputStream = null;
        String[] certFiles = {
            "/cert/io661.com_bundle.crt",
            "/cert/fullchain.pem",
            "/cert/io661.com.crt"
        };

        for (String certFile : certFiles) {
            certInputStream = SSLCertificateManager.class.getResourceAsStream(certFile);
            if (certInputStream != null) {
                System.out.println("找到证书文件: " + certFile);
                break;
            }
        }

        if (certInputStream == null) {
            // 如果资源文件不存在，尝试从文件系统加载
            String[] filePaths = {
                "src/main/resources/cert/io661.com_bundle.crt",
                "src/main/resources/cert/fullchain.pem",
                "src/main/resources/cert/io661.com.crt"
            };

            for (String filePath : filePaths) {
                try {
                    certInputStream = new FileInputStream(filePath);
                    System.out.println("从文件系统加载证书: " + filePath);
                    break;
                } catch (Exception e) {
                    // 继续尝试下一个文件
                }
            }
        }

        if (certInputStream == null) {
            throw new Exception("无法找到任何证书文件");
        }

        // 读取所有证书（证书链）
        java.util.Collection<? extends Certificate> certificates = cf.generateCertificates(certInputStream);
        certInputStream.close();

        // 获取默认的TrustStore
        KeyStore defaultKeyStore = KeyStore.getInstance(KeyStore.getDefaultType());
        defaultKeyStore.load(null, null);

        // 获取系统默认的TrustManager
        TrustManagerFactory defaultTmf = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
        defaultTmf.init((KeyStore) null); // 使用系统默认的TrustStore

        // 创建新的KeyStore，包含系统证书和自定义证书
        KeyStore combinedKeyStore = KeyStore.getInstance(KeyStore.getDefaultType());
        combinedKeyStore.load(null, null);

        // 添加自定义证书
        int i = 0;
        for (Certificate cert : certificates) {
            combinedKeyStore.setCertificateEntry("custom_cert" + i, cert);
            i++;
            System.out.println("已添加自定义证书: " + ((X509Certificate) cert).getSubjectX500Principal());
        }

        // 创建组合的TrustManager
        TrustManagerFactory tmf = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
        tmf.init(combinedKeyStore);

        // 创建SSL上下文
        sslContext = SSLContext.getInstance("TLS");
        sslContext.init(null, tmf.getTrustManagers(), null);

        initialized = true;
        System.out.println("SSL证书配置成功，共加载 " + certificates.size() + " 个自定义证书");
        return true;

        } catch (Exception e) {
            System.err.println("加载自定义证书失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 创建信任所有证书的SSL上下文（仅用于开发环境）
     */
    private static void createTrustAllSSLContext() throws Exception {
        TrustManager[] trustAllCerts = new TrustManager[] {
            new X509TrustManager() {
                public X509Certificate[] getAcceptedIssuers() {
                    return null;
                }
                public void checkClientTrusted(X509Certificate[] certs, String authType) {
                }
                public void checkServerTrusted(X509Certificate[] certs, String authType) {
                }
            }
        };
        
        sslContext = SSLContext.getInstance("TLS");
        sslContext.init(null, trustAllCerts, new java.security.SecureRandom());
        initialized = true;
    }
    
    /**
     * 获取配置好的SSL上下文
     */
    public static SSLContext getSSLContext() {
        if (!initialized) {
            initializeSSLContext();
        }
        return sslContext;
    }
    
    /**
     * 获取配置好的SSLSocketFactory
     */
    public static SSLSocketFactory getSSLSocketFactory() {
        return getSSLContext().getSocketFactory();
    }
    
    /**
     * 获取配置好的HostnameVerifier
     */
    public static HostnameVerifier getHostnameVerifier() {
        return (hostname, session) -> {
            // 检查是否在信任域名列表中
            if (trustedDomains.contains(hostname)) {
                System.out.println("信任域名: " + hostname);
                return true;
            }

            // 检查是否是信任域名的子域名
            for (String trustedDomain : trustedDomains) {
                if (hostname.endsWith("." + trustedDomain)) {
                    System.out.println("信任子域名: " + hostname + " (基于: " + trustedDomain + ")");
                    return true;
                }
            }

            // 对于其他域名，使用默认验证
            return HttpsURLConnection.getDefaultHostnameVerifier().verify(hostname, session);
        };
    }

    /**
     * 检查是否启用了全局SSL配置
     */
    public static boolean isGlobalSSLEnabled() {
        return globalSSLEnabled;
    }

    /**
     * 获取当前信任的域名列表
     */
    public static Set<String> getTrustedDomains() {
        return new HashSet<>(trustedDomains);
    }
}
