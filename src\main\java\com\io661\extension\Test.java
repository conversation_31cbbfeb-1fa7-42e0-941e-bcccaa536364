package com.io661.extension;

import com.io661.extension.commonURL.CommonYouPinHttpUrl;
import com.io661.extension.model.Steam.GetTradingListReq;
import com.io661.extension.model.Steam.IO661TradeOrdersReq;
import com.io661.extension.service.Impl.TransactionAssistantServiceImpl;
import com.io661.extension.service.Impl.UserServiceImpl;
import com.io661.extension.service.Impl.YouPinServiceImpl;
import com.io661.extension.util.YouPin.YouPinCookieManager;

import java.io.IOException;

import static com.io661.extension.util.User.UserCookieManager.readToken;

public class Test {
    public static void main(String[] args) throws IOException {
        CommonYouPinHttpUrl youPinHttpClient = new CommonYouPinHttpUrl();
        String steamId = "*****************";

        YouPinServiceImpl youPinService = new YouPinServiceImpl();
        TransactionAssistantServiceImpl transactionAssistantService = new TransactionAssistantServiceImpl();
        UserServiceImpl userService = new UserServiceImpl();


        // buffcookies
//        String session = startGetBuffCookies();
//
//        System.out.println(session);


//        System.out.println(youPinService.sendSmsCode("***********"));


//        System.out.println(youPinService.loginAccount("***********", ""));


        String token = YouPinCookieManager.readYouPinCookie(steamId);

        String userToken = readToken();

        System.out.println("获取token:" + token);

//        System.out.println(queryYouPinCookie("*****************"));
//        System.out.println(youPinService.getUserInfo(token));
//        System.out.println(youPinService.getUserInventoryDataList(token));

//        System.out.println(youPinService.getUserInventoryOnSellDataList(token));

//        System.out.println(Arrays.toString(youPinService.getGoodsInfo(token)));


//        System.out.println(youPinService.userSellInventory(token));

//        System.out.println(youPinService.userItemsOnSellPriceChange(token));

//        System.out.println(youPinService.userItemsOffSale(token));

//        System.out.println(Arrays.toString(youPinService.getGoodsInfo(token)));



//        IO661TradeOrdersReq req = new IO661TradeOrdersReq();
//        req.setType(2L);


//        System.out.println(transactionAssistantService.getAllOrders(req));

//        GetTradingListReq getTradingListReq = new GetTradingListReq();
//        getTradingListReq.setSteamId(steamId);


//        System.out.println(transactionAssistantService.getAllTradeOffers(getTradingListReq));
        System.out.println(userService.getUserInfo(userToken));
        System.out.println("^_^");


    }
}
