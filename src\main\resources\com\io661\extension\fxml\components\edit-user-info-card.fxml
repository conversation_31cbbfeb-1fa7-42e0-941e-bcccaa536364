<?xml version="1.0" encoding="UTF-8"?>

<?import java.net.URL?>
<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.ColumnConstraints?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.layout.RowConstraints?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.VBox?>

<AnchorPane prefHeight="300.0" prefWidth="450.0"
            xmlns="http://javafx.com/javafx/21"
            xmlns:fx="http://javafx.com/fxml/1"
            fx:controller="com.io661.extension.controller.UserController">
    <stylesheets>
        <URL value="@../../css/edit-user-info-card.css" />
    </stylesheets>

    <VBox spacing="15" AnchorPane.leftAnchor="15.0" AnchorPane.rightAnchor="15.0" AnchorPane.topAnchor="15.0" AnchorPane.bottomAnchor="15.0">
        <!-- 标题 -->
        <HBox alignment="CENTER_LEFT" spacing="10">
            <Label text="个人资料" styleClass="title-label" />
        </HBox>

        <!-- 使用GridPane实现3列4行布局 -->
        <GridPane hgap="15" vgap="15" alignment="CENTER">
            <columnConstraints>
                <ColumnConstraints minWidth="100" prefWidth="100" />
                <ColumnConstraints minWidth="200" prefWidth="200" />
                <ColumnConstraints minWidth="100" prefWidth="100" />
            </columnConstraints>
            <rowConstraints>
                <RowConstraints minHeight="50" prefHeight="50" vgrow="SOMETIMES" />
                <RowConstraints minHeight="40" prefHeight="40" vgrow="SOMETIMES" />
                <RowConstraints minHeight="40" prefHeight="40" vgrow="SOMETIMES" />
                <RowConstraints minHeight="40" prefHeight="40" vgrow="SOMETIMES" />
            </rowConstraints>

            <!-- 第一行：头像 -->
            <Label text="头像" styleClass="info-label" GridPane.columnIndex="0" GridPane.rowIndex="0" GridPane.halignment="CENTER" GridPane.valignment="CENTER" />
            <ImageView fx:id="profileImage" fitHeight="40.0" fitWidth="40.0" preserveRatio="true" GridPane.columnIndex="1" GridPane.rowIndex="0" GridPane.halignment="CENTER" GridPane.valignment="CENTER" />
            <Button fx:id="editAvatar" styleClass="action-button" text="修改头像" prefWidth="90" GridPane.columnIndex="2" GridPane.rowIndex="0" GridPane.halignment="CENTER" GridPane.valignment="CENTER" />

            <!-- 第二行：昵称 -->
            <Label text="昵称" styleClass="info-label" GridPane.columnIndex="0" GridPane.rowIndex="1" GridPane.halignment="CENTER" GridPane.valignment="CENTER" />
            <Label fx:id="nicknameLabel" styleClass="value-label" text="用户昵称" GridPane.columnIndex="1" GridPane.rowIndex="1" GridPane.halignment="CENTER" GridPane.valignment="CENTER" />
            <Button fx:id="editNickName" styleClass="action-button" text="修改昵称" prefWidth="90" GridPane.columnIndex="2" GridPane.rowIndex="1" GridPane.halignment="CENTER" GridPane.valignment="CENTER" />

            <!-- 第三行：UID -->
            <Label text="UID" styleClass="info-label" GridPane.columnIndex="0" GridPane.rowIndex="2" GridPane.halignment="CENTER" GridPane.valignment="CENTER" />
            <Label fx:id="uidLabel" styleClass="value-label" text="用户ID" GridPane.columnIndex="1" GridPane.rowIndex="2" GridPane.halignment="CENTER" GridPane.valignment="CENTER" />

            <!-- 第四行：绑定手机号 -->
            <Label text="绑定手机号" styleClass="info-label" GridPane.columnIndex="0" GridPane.rowIndex="3" GridPane.halignment="CENTER" GridPane.valignment="CENTER" />
            <Label fx:id="phoneLabel" styleClass="value-label" text="手机号" GridPane.columnIndex="1" GridPane.rowIndex="3" GridPane.halignment="CENTER" GridPane.valignment="CENTER" />
        </GridPane>

        <!-- 实名状态区域（单独一行） -->
        <HBox alignment="CENTER" spacing="15" minHeight="40">
            <Label text="实名状态" styleClass="info-label" prefWidth="100" />
            <Label fx:id="realNameStatusLabel" styleClass="value-label" text="未实名" prefWidth="200" />
        </HBox>

        <!-- 昵称编辑字段（初始隐藏） -->
        <HBox alignment="CENTER" spacing="15" fx:id="nicknameEditBox" visible="false" managed="false" minHeight="40">
            <Label text="昵称" styleClass="info-label" prefWidth="100" />
            <TextField fx:id="nicknameField" prefWidth="200" />
            <HBox spacing="10" alignment="CENTER">
                <Button fx:id="saveButton" onAction="#handleSaveUserInfo" styleClass="action-button" text="保存" prefWidth="45" />
                <Button fx:id="cancelButton" onAction="#handleCancelEdit" styleClass="action-button" text="取消" prefWidth="45" />
            </HBox>
        </HBox>

        <!-- 关闭按钮 -->
        <HBox alignment="CENTER" spacing="10">
            <Button fx:id="closeButton" onAction="#handleCloseDialog" styleClass="action-button" text="关闭" prefWidth="80" />
        </HBox>
    </VBox>
</AnchorPane>
