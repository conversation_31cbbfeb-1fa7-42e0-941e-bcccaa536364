/* IO661订单列表样式 */


/* 筛选容器 */

.filter-container {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #e9ecef;
    -fx-border-width: 0 0 1 0;
}

.filter-label {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
}

.search-container {
    -fx-background-color: white;
    -fx-background-radius: 6px;
    -fx-border-color: #ced4da;
    -fx-border-radius: 6px;
    -fx-border-width: 1px;
    -fx-padding: 2px;
}

.search-field {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-font-size: 14px;
}

.search-button {
    -fx-background-color: #007bff;
    -fx-text-fill: white;
    -fx-font-size: 12px;
    -fx-padding: 5 10;
    -fx-background-radius: 4px;
    -fx-border-radius: 4px;
}

.search-button:hover {
    -fx-background-color: #0056b3;
}

.refresh-button {
    -fx-background-color: #28a745;
    -fx-text-fill: white;
    -fx-font-size: 12px;
    -fx-padding: 5 10;
    -fx-background-radius: 4px;
    -fx-border-radius: 4px;
}

.refresh-button:hover {
    -fx-background-color: #1e7e34;
}

.summary-label {
    -fx-font-size: 14px;
    -fx-text-fill: #6c757d;
}


/* 订单列表容器 */

.order-scroll-pane {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
}

.order-list-container {
    -fx-background-color: #ffffff;
}


/* 订单卡片 */

.order-card {
    -fx-background-color: white;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-effect: dropshadow(three-pass-box, rgba(0, 0, 0, 0.1), 2, 0, 0, 1);
}

.order-card:hover {
    -fx-border-color: #007bff;
    -fx-effect: dropshadow(three-pass-box, rgba(0, 123, 255, 0.2), 4, 0, 0, 2);
}


/* 订单卡片内容对齐 */

.order-card .item-name-label {
    -fx-max-width: 250px;
    -fx-pref-width: 250px;
}

.order-no-label {
    -fx-font-size: 12px;
    -fx-text-fill: #6c757d;
    -fx-font-family: "Consolas", "Monaco", monospace;
}

.item-name-label {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-text-fill: #212529;
    -fx-wrap-text: true;
}

.hash-name-label {
    -fx-font-size: 12px;
    -fx-text-fill: #6c757d;
    -fx-wrap-text: true;
}

.price-label {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-text-fill: #28a745;
}

.terminal-price-label {
    -fx-font-size: 13px;
    -fx-text-fill: #17a2b8;
}

.time-label {
    -fx-font-size: 12px;
    -fx-text-fill: #6c757d;
}

.float-label {
    -fx-font-size: 12px;
    -fx-text-fill: #6c757d;
    -fx-font-family: "Consolas", "Monaco", monospace;
}

.detail-button {
    -fx-background-color: #007bff;
    -fx-text-fill: white;
    -fx-font-size: 12px;
    -fx-padding: 6 12;
    -fx-background-radius: 4px;
    -fx-border-radius: 4px;
}

.detail-button:hover {
    -fx-background-color: #0056b3;
}


/* 状态标签 */

.status-label {
    -fx-font-size: 12px;
    -fx-font-weight: bold;
    -fx-padding: 3 8;
    -fx-background-radius: 12px;
    -fx-border-radius: 12px;
}

.status-waiting {
    -fx-background-color: #fff3cd;
    -fx-text-fill: #856404;
    -fx-border-color: #ffeaa7;
}

.status-pending {
    -fx-background-color: #d1ecf1;
    -fx-text-fill: #0c5460;
    -fx-border-color: #bee5eb;
}

.status-confirming {
    -fx-background-color: #cce5ff;
    -fx-text-fill: #004085;
    -fx-border-color: #99d6ff;
}

.status-success {
    -fx-background-color: #d4edda;
    -fx-text-fill: #155724;
    -fx-border-color: #c3e6cb;
}

.status-failed {
    -fx-background-color: #f8d7da;
    -fx-text-fill: #721c24;
    -fx-border-color: #f5c6cb;
}

.status-unknown {
    -fx-background-color: #e2e3e5;
    -fx-text-fill: #383d41;
    -fx-border-color: #d6d8db;
}


/* 加载和无数据状态 */

.loading-label {
    -fx-font-size: 14px;
    -fx-text-fill: #6c757d;
}

.no-data-label {
    -fx-font-size: 16px;
    -fx-text-fill: #6c757d;
}


/* 订单详情弹窗样式 */

.order-detail-dialog {
    -fx-background-color: white;
}

.dialog-header {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #e9ecef;
    -fx-border-width: 0 0 1 0;
}

.dialog-title {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: #212529;
}

.close-button {
    -fx-background-color: transparent;
    -fx-text-fill: #6c757d;
    -fx-font-size: 20px;
    -fx-font-weight: bold;
    -fx-padding: 5 10;
    -fx-background-radius: 50%;
    -fx-border-radius: 50%;
}

.close-button:hover {
    -fx-background-color: #f8f9fa;
    -fx-text-fill: #495057;
}

.detail-scroll-pane {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
}

.detail-container {
    -fx-background-color: white;
}

.info-section {
    -fx-background-color: #f8f9fa;
    -fx-padding: 15px;
    -fx-background-radius: 8px;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
}

.section-title {
    -fx-font-size: 16px;
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
}

.info-grid {
    -fx-hgap: 15px;
    -fx-vgap: 10px;
}

.info-label {
    -fx-font-size: 14px;
    -fx-text-fill: #6c757d;
    -fx-font-weight: bold;
}

.info-value {
    -fx-font-size: 14px;
    -fx-text-fill: #212529;
}

.item-image {
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px;
}

.item-name {
    -fx-font-size: 16px;
    -fx-font-weight: bold;
    -fx-text-fill: #212529;
    -fx-wrap-text: true;
}

.item-hash-name {
    -fx-font-size: 12px;
    -fx-text-fill: #6c757d;
    -fx-wrap-text: true;
}

.quality-tag,
.rarity-tag,
.exterior-tag {
    -fx-font-size: 11px;
    -fx-padding: 2 6;
    -fx-background-radius: 10px;
    -fx-border-radius: 10px;
    -fx-background-color: #e9ecef;
    -fx-text-fill: #495057;
}

.user-avatar {
    -fx-background-radius: 25px;
    -fx-border-radius: 25px;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px;
}

.user-nickname {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-text-fill: #212529;
}

.dialog-footer {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1 0 0 0;
}

.primary-button {
    -fx-background-color: #007bff;
    -fx-text-fill: white;
    -fx-font-size: 14px;
    -fx-padding: 8 16;
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
}

.primary-button:hover {
    -fx-background-color: #0056b3;
}

.secondary-button {
    -fx-background-color: #6c757d;
    -fx-text-fill: white;
    -fx-font-size: 14px;
    -fx-padding: 8 16;
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
}

.secondary-button:hover {
    -fx-background-color: #545b62;
}


/* 新的订单列表样式 - 表格式布局 */

.order-header {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #e9ecef;
    -fx-border-width: 0 0 1 0;
}

.header-label {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
    -fx-text-alignment: center;
    -fx-alignment: center;
}

.trade-type-label {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
}


/* 进度条样式 */

.progress-container {
    -fx-alignment: center;
    -fx-padding: 20 0;
}

.progress-indicator {
    -fx-background-color: #e9ecef;
    -fx-text-fill: #6c757d;
    -fx-background-radius: 15px;
    -fx-border-radius: 15px;
    -fx-border-color: #ced4da;
    -fx-border-width: 2px;
    -fx-font-size: 12px;
    -fx-font-weight: bold;
}

.progress-completed {
    -fx-background-color: #28a745;
    -fx-text-fill: white;
    -fx-border-color: #28a745;
}

.progress-current {
    -fx-background-color: #007bff;
    -fx-text-fill: white;
    -fx-border-color: #007bff;
}

.progress-pending {
    -fx-background-color: #f8f9fa;
    -fx-text-fill: #6c757d;
    -fx-border-color: #ced4da;
}

.progress-failed {
    -fx-background-color: #dc3545;
    -fx-text-fill: white;
    -fx-border-color: #dc3545;
}

.progress-step-label {
    -fx-font-size: 12px;
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
    -fx-text-alignment: center;
}

.progress-time-label {
    -fx-font-size: 10px;
    -fx-text-fill: #6c757d;
    -fx-text-alignment: center;
}

.progress-line {
    -fx-background-color: #e9ecef;
    -fx-pref-height: 2px;
    -fx-max-height: 2px;
    -fx-min-height: 2px;
}

.progress-line-completed {
    -fx-background-color: #28a745;
}

.progress-line-pending {
    -fx-background-color: #e9ecef;
}