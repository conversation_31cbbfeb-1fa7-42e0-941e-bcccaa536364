package com.io661.extension.test;

import com.io661.extension.commonURL.CommonHttpUrl;
import com.io661.extension.util.SSLCertificateManager;

import java.io.IOException;

/**
 * SSL证书配置测试类
 * 用于测试HTTPS连接是否能正常工作
 */
public class SSLTestMain {
    
    public static void main(String[] args) {
        System.out.println("开始测试SSL证书配置...");
        
        // 初始化SSL证书管理器
        SSLCertificateManager.initializeSSLContext();
        
        // 创建HTTP客户端
        CommonHttpUrl httpClient = new CommonHttpUrl();
        
        try {
            // 测试GET请求
            System.out.println("测试GET请求到 https://io661.com/");
            String response = httpClient.doGet("", null, null);
            System.out.println("GET请求成功！响应长度: " + response.length());
            System.out.println("响应前100个字符: " + response.substring(0, Math.min(100, response.length())));

            // 测试API端点（如果存在）
            System.out.println("\n测试API端点...");
            try {
                String apiResponse = httpClient.doGet("api/test", null, null);
                System.out.println("API请求成功！响应长度: " + apiResponse.length());
            } catch (IOException apiEx) {
                System.out.println("API端点不存在或无法访问（这是正常的）: " + apiEx.getMessage());
            }

        } catch (IOException e) {
            System.err.println("HTTPS请求失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("SSL证书配置测试完成。");
    }
}
