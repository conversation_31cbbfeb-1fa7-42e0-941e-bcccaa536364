.user-info-card {
    -fx-background-color: white;
    -fx-background-radius: 12px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.15), 12, 0, 0, 2);
}

.user-info-section {
    -fx-background-color: transparent;
}

.user-avatar {
    -fx-background-radius: 40px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.1), 4, 0, 0, 1);
}

.user-nickname {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: #333333;
}

.user-balance {
    -fx-font-size: 16px;
    -fx-font-weight: bold;
    -fx-text-fill: #1890ff;
}

.withdraw-button {
    -fx-background-color: #52c41a;
    -fx-text-fill: white;
    -fx-background-radius: 6px;
    -fx-padding: 6 12;
    -fx-font-size: 12px;
    -fx-cursor: hand;
}

.withdraw-button:hover {
    -fx-background-color: #73d13d;
}

.function-section {
    -fx-background-color: transparent;
}

.menu-button {
    -fx-background-color: #f5f5f5;
    -fx-text-fill: #333333;
    -fx-background-radius: 6px;
    -fx-padding: 10 15;
    -fx-font-size: 14px;
    -fx-cursor: hand;
    -fx-alignment: center-left;
}

.menu-button:hover {
    -fx-background-color: #e6f7ff;
    -fx-text-fill: #1890ff;
}

.menu-button {
    -fx-background-color: transparent;
    -fx-text-fill: #333333;
    -fx-alignment: CENTER_LEFT;
    -fx-cursor: hand;
    -fx-padding: 10px;
}

.menu-button:hover {
    -fx-background-color: #f5f5f5;
}

.menu-button:pressed {
    -fx-background-color: #e0e0e0;
}