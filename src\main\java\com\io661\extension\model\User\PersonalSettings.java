package com.io661.extension.model.User;

import lombok.Data;

/**
 * 个人设置模型类
 */
@Data
public class PersonalSettings {
    /**
     * 主键ID
     */
    private int id;

    /**
     * IO661平台交易费率
     */
    private double io661TransRatio;

    /**
     * IO661平台提现费率
     */
    private double io661WithdrawRatio;

    /**
     * 悠悠有品平台交易费率
     */
    private double youPinTransRatio;

    /**
     * 悠悠有品平台提现费率
     */
    private double youPinWithdrawRatio;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 默认构造函数
     */
    public PersonalSettings() {
        this.id = 1;
        this.io661TransRatio = 0.05;
        this.io661WithdrawRatio = 0.05;
        this.youPinTransRatio = 0.10;
        this.youPinWithdrawRatio = 0.10;
    }

    /**
     * 带参数的构造函数
     */
    public PersonalSettings(double io661TransRatio, double io661WithdrawRatio, 
                           double youPinTransRatio, double youPinWithdrawRatio) {
        this.id = 1;
        this.io661TransRatio = io661TransRatio;
        this.io661WithdrawRatio = io661WithdrawRatio;
        this.youPinTransRatio = youPinTransRatio;
        this.youPinWithdrawRatio = youPinWithdrawRatio;
    }

    /**
     * 验证费率是否在有效范围内
     * @return 是否有效
     */
    public boolean isValid() {
        return io661TransRatio >= 0 && io661TransRatio <= 1 &&
               io661WithdrawRatio >= 0 && io661WithdrawRatio <= 1 &&
               youPinTransRatio >= 0 && youPinTransRatio <= 1 &&
               youPinWithdrawRatio >= 0 && youPinWithdrawRatio <= 1;
    }

    /**
     * 重置为默认值
     */
    public void resetToDefault() {
        this.io661TransRatio = 0.05;
        this.io661WithdrawRatio = 0.05;
        this.youPinTransRatio = 0.10;
        this.youPinWithdrawRatio = 0.10;
    }

    @Override
    public String toString() {
        return "PersonalSettings{" +
                "id=" + id +
                ", io661TransRatio=" + io661TransRatio +
                ", io661WithdrawRatio=" + io661WithdrawRatio +
                ", youPinTransRatio=" + youPinTransRatio +
                ", youPinWithdrawRatio=" + youPinWithdrawRatio +
                ", createTime='" + createTime + '\'' +
                ", updateTime='" + updateTime + '\'' +
                '}';
    }
}
