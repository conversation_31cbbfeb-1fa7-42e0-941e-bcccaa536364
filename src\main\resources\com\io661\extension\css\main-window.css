/* 菜单项样式 */
.menu-item {
    -fx-background-color: transparent;
    -fx-border-radius: 10px;
    -fx-background-radius: 10px;
    -fx-padding: 5;
    -fx-spacing: 5;
    -fx-cursor: hand;
    -fx-min-width: 80px;
    -fx-max-width: 80px;
    -fx-border-width: 0;
    -fx-border-color: transparent;
}

.menu-item:hover {
    -fx-background-color: #e0e0e0;
    -fx-background-radius: 5;
}

.menu-item.selected {
    -fx-background-color: #d0d0d0;
    -fx-border-color: #4a90e2;
    -fx-border-width: 0 0 0 4px;
    -fx-border-radius: 0;
}

.menu-text {
    -fx-font-size: 12px;
    -fx-text-alignment: center;
    -fx-alignment: center;
    -fx-padding: 5 0 0 0;
    -fx-text-fill: #333333;
}

/* 确保内容区域填满可用空间 */
.root {
    -fx-background-color: white;
}

#contentArea {
    -fx-background-color: white;
    -fx-padding: 0;
    -fx-min-width: 100%;
    -fx-min-height: 100%;
}

.user-info-item {
    -fx-padding: 5;
    -fx-cursor: hand;
    -fx-background-radius: 5;
    -fx-border-radius: 5;
}

.user-info-item:hover {
    -fx-background-color: #e0e0e0;
}

.user-text {
    -fx-font-size: 12px;
    -fx-text-fill: #333333;
    -fx-font-weight: bold;
}

#userAvatarIcon {
    -fx-background-radius: 20;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 5, 0, 0, 0);
}