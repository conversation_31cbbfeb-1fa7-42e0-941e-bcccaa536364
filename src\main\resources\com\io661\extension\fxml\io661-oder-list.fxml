<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.*?>
<BorderPane xmlns:fx="http://javafx.com/fxml/1" stylesheets="@../css/order-styles.css" xmlns="http://javafx.com/javafx/23.0.1" fx:controller="com.io661.extension.controller.IO661OrderListController">
   <top>
      <!-- 筛选和搜索区域 -->
      <VBox spacing="10" styleClass="filter-container">
         <padding>
            <Insets bottom="10" left="15" right="15" top="15" />
         </padding>

         <!-- 第一行：订单类型和状态筛选 -->
         <HBox alignment="CENTER_LEFT" spacing="15">
            <Label styleClass="filter-label" text="订单类型:" />
            <ComboBox fx:id="orderTypeComboBox" prefWidth="120" promptText="全部" />

            <Label styleClass="filter-label" text="订单状态:" />
            <ComboBox fx:id="orderStatusComboBox" prefWidth="150" promptText="全部状态" />

            <Label styleClass="filter-label" text="Steam账号:" />
            <ComboBox fx:id="steamAccountComboBox" prefWidth="180" promptText="请选择Steam账号" />

            <Region HBox.hgrow="ALWAYS" />

            <!-- 搜索框 -->
            <HBox alignment="CENTER" spacing="5" styleClass="search-container">
               <TextField fx:id="searchField" prefWidth="200" promptText="搜索饰品/订单号..." styleClass="search-field" />
               <Button fx:id="searchButton" styleClass="search-button" text="搜索" />
            </HBox>
         </HBox>

         <!-- 第二行：统计信息 -->
         <HBox alignment="CENTER_LEFT" spacing="10">
            <Label fx:id="orderSummaryLabel" styleClass="summary-label" text="共 0 个订单" />
            <Region HBox.hgrow="ALWAYS" />
            <Button fx:id="refreshButton" styleClass="refresh-button" text="刷新" />
         </HBox>
      </VBox>
   </top>

   <center>
      <!-- 订单列表区域 -->
      <VBox>
         <!-- 表头 -->
         <HBox spacing="15" styleClass="order-header">
            <padding>
               <Insets bottom="10" left="25" right="25" top="10" />
            </padding>

            <Label prefWidth="280" styleClass="header-label" text="订单信息" />
            <Label prefWidth="280" styleClass="header-label" text="物品" />
            <Label prefWidth="70" styleClass="header-label" text="类型" />
            <Label prefWidth="150" styleClass="header-label" text="实际收入" />
            <Label prefWidth="150" styleClass="header-label" text="状态" style="-fx-text-alignment: center"/>
            <Region HBox.hgrow="ALWAYS" />
            <Label prefWidth="120" styleClass="header-label" text="操作" />
         </HBox>

         <StackPane VBox.vgrow="ALWAYS">
            <!-- 订单列表 -->
            <ScrollPane fx:id="orderScrollPane" fitToWidth="true" styleClass="order-scroll-pane">
               <content>
                  <VBox fx:id="orderListContainer" spacing="5" styleClass="order-list-container">
                     <padding>
                        <Insets bottom="10" left="10" right="10" top="10" />
                     </padding>
                  </VBox>
               </content>
            </ScrollPane>

            <!-- 加载指示器 -->
            <StackPane fx:id="loadingPane" style="-fx-background-color: rgba(255, 255, 255, 0.8);" visible="false">
               <VBox alignment="CENTER" spacing="10">
                  <ProgressIndicator prefHeight="50" prefWidth="50" />
                  <Label styleClass="loading-label" text="加载中..." />
               </VBox>
            </StackPane>

            <!-- 无数据提示 -->
            <VBox fx:id="noDataPane" alignment="CENTER" spacing="10" visible="false">
               <ImageView fitHeight="64" fitWidth="64" opacity="0.5">
                  <Image url="@../img/no-data.png" />
               </ImageView>
               <Label styleClass="no-data-label" text="暂无订单数据" />
            </VBox>
         </StackPane>
      </VBox>
   </center>
</BorderPane>
