package com.io661.extension.model.Steam;

import lombok.Data;

import java.util.List;

@Data
public class IO661TradeOfferRes {
    private int code;
    private String msg;
    private Data_ data;

    @Data
    public static class Data_ {
        private int pages;
        private int total;
        private List<ListItem> list;
    }

    @Data
    public static class ListItem {
        private String orderNo;
        private String iconUrl;           // 图标URL
        private String hashName;          // 哈希名称
        private String itemName;          // 物品名称
        private Double floatValue;        // 磨损值（可为null）
        private List<Sticker> stickerList; // 印花列表（可为null）
        private Double price;             // 价格
        private Double terminalPrice;     // 终端价格
        private int status;               // 状态码
        private String statusName;        // 状态名称
        private String createTime;        // 创建时间
    }

    @Data
    public static class Sticker {
        private String hashName;  // 印花哈希名称
        private String itemName;  // 印花名称
        private int slot;         // 槽位
        private int wear;         // 磨损度
        private String iconUrl;   // 印花图标URL
    }
}