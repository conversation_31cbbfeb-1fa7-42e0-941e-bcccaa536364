# 全局SSL配置指南

## 概述

本指南介绍如何配置全局SSL设置，以支持多域名HTTPS访问和动态URL处理。

## 功能特性

### ✅ 已实现的功能

1. **全局SSL配置**
   - 一次配置，全局生效
   - 自动应用到所有HTTPS连接
   - 支持动态添加信任域名

2. **多域名支持**
   - 支持主域名和子域名
   - 动态域名验证
   - 批量添加信任域名

3. **动态URL处理**
   - 支持从API响应中提取URL
   - 自动处理HTTPS连接
   - 统一的HTTP客户端工具

4. **灵活配置**
   - 可启用/禁用全局SSL
   - 支持单独连接的SSL配置
   - 兼容HTTP和HTTPS

## 使用方法

### 1. 启用全局SSL配置

```java
// 在应用程序启动时
SSLCertificateManager.enableGlobalSSL();
```

### 2. 添加信任域名

```java
// 单个域名
SSLCertificateManager.addTrustedDomain("example.com");

// 批量添加
SSLCertificateManager.addTrustedDomains(
    "io661.com",
    "s3.io661.com", 
    "api.io661.com"
);
```

### 3. 发送HTTPS请求

```java
// 使用HttpClientUtil发送请求
String response = HttpClientUtil.doGet("https://api.io661.com/data", headers);
String postResponse = HttpClientUtil.doPost("https://api.io661.com/submit", jsonBody, headers);
```

### 4. 处理动态URL

```java
// 从响应数据中提取并请求URL
String responseData = HttpClientUtil.doGet("https://api.io661.com/config", null);
// responseData可能包含其他HTTPS URL，会自动使用SSL配置
```

## 配置示例

### 应用程序初始化

```java
public class MyApplication {
    public void initialize() {
        // 添加所有需要的域名
        SSLCertificateManager.addTrustedDomains(
            "io661.com",
            "open.io661.com", 
            "s3.io661.com",
            "api.io661.com",
            "cdn.io661.com",
            "static.io661.com",
            "img.io661.com"
        );
        
        // 启用全局SSL配置
        SSLCertificateManager.enableGlobalSSL();
        
        System.out.println("全局SSL配置已启用");
    }
}
```

### HTTP客户端使用

```java
public class ApiClient {
    public void fetchData() throws IOException {
        // 这些请求会自动使用全局SSL配置
        String userData = HttpClientUtil.doGet("https://api.io661.com/user", headers);
        String imageData = HttpClientUtil.doGet("https://s3.io661.com/images/photo.jpg", headers);
        String configData = HttpClientUtil.doGet("https://cdn.io661.com/config.json", headers);
    }
}
```

## 支持的域名

当前配置支持以下域名模式：

- `io661.com`
- `*.io661.com` (所有子域名)
- 可动态添加其他域名

## 证书处理

1. **自定义证书**: 优先加载项目中的证书文件
2. **信任所有证书**: 如果自定义证书加载失败，使用信任所有证书模式
3. **域名验证**: 基于信任域名列表进行验证

## 性能优化

1. **一次初始化**: SSL配置只在应用启动时初始化一次
2. **全局生效**: 避免每个连接重复配置SSL
3. **智能判断**: 只对HTTPS连接应用SSL配置

## 故障排除

### 常见问题

1. **SSL握手失败**
   ```
   解决方案：确保域名已添加到信任列表
   SSLCertificateManager.addTrustedDomain("your-domain.com");
   ```

2. **证书验证失败**
   ```
   解决方案：检查证书文件是否存在且有效
   ```

3. **HTTP连接失败**
   ```
   解决方案：HTTP连接不受SSL配置影响，检查网络连接
   ```

### 调试信息

启用调试输出：
```java
System.setProperty("javax.net.debug", "ssl");
```

### 检查配置状态

```java
// 检查是否启用全局SSL
boolean enabled = SSLCertificateManager.isGlobalSSLEnabled();

// 获取信任域名列表
Set<String> domains = SSLCertificateManager.getTrustedDomains();
```

## 安全注意事项

1. **生产环境**: 建议使用具体的证书验证而不是信任所有证书
2. **域名限制**: 只添加真正需要的域名到信任列表
3. **证书更新**: 定期检查和更新证书文件

## 测试

运行测试类验证配置：
```bash
java -cp "target/classes" com.io661.extension.test.GlobalSSLTestMain
```

## 总结

全局SSL配置提供了一个统一、灵活的解决方案来处理多域名HTTPS访问。通过一次配置，可以支持：

- ✅ 多个域名的HTTPS访问
- ✅ 动态URL的自动处理  
- ✅ API响应中包含的HTTPS资源
- ✅ 图片、CSS、JS等静态资源的HTTPS加载
- ✅ 灵活的域名管理和配置
