package com.io661.extension.model.Steam;

import lombok.Data;

@Data
public class IO661OrderStatusRes {
    private int code;
    private String msg;
    private Data_ data;

    @Data
    public static class Data_ {
        private int type;
        private double price;
        private double terminalPrice;
        private double fee;
        private int status;
        private String statusDes;
        private UserInfo buyerInfo;
        private UserInfo sellerInfo;
        private TemplateInfo templateInfo;
        private double floatValue;
        private String updateTime;
        private String createTime;
        private String orderNo;
    }

    @Data
    public static class UserInfo {
        private String avatar;
        private String nickname;
        private String regDate;
    }

    @Data
    public static class TemplateInfo {
        private String itemName;
        private String hashName;
        private String tagQuality;
        private String tagRarity;
        private String tagExterior;
        private String iconUrl;
    }
}