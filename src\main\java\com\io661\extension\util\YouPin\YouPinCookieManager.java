package com.io661.extension.util.YouPin;

import com.io661.extension.service.Impl.YouPinServiceImpl;
import com.io661.extension.util.SQLite.SqliteManager;

import java.io.IOException;

/**
 * Youpin cookie 管理类
 * 专门用于管理 youpin898.com 的 cookie，使用 SQLite 数据库存储。
 */
public class YouPinCookieManager {

    // 不再需要文件路径相关的常量和静态初始化块

    /**
     * 保存Youpin cookie到SQLite数据库。
     * 如果该 steamId 已存在cookie，则会更新。
     * @param steamId Steam用户的唯一标识符。
     * @param cookieValue 要保存的Youpin Cookie (authorization token)。
     * @return 操作是否成功。
     */
    public static boolean saveYouPinCookie(String steamId, String cookieValue) {
        if (steamId == null || steamId.trim().isEmpty()) {
            System.err.println("Steam ID 不能为空，无法保存YouPin Cookie。");
            return false;
        }
        if (cookieValue == null) { // 允许空字符串来表示“清除”或“无cookie”
            System.err.println("YouPin Cookie 值不能为 null，无法保存。");
            return false;
        }

        boolean success = SqliteManager.upsertYouPinCookie(steamId, cookieValue);
        if (success) {
            System.out.println("YouPin Cookie for steamId '" + steamId + "' 已成功保存/更新到数据库。");
        } else {
            System.err.println("保存/更新YouPin Cookie for steamId '" + steamId + "' 到数据库失败。");
        }
        return success;
    }

    /**
     * 从SQLite数据库读取指定 steamId 的YouPin Cookie。
     * @param steamId Steam用户的唯一标识符。
     * @return YouPin Cookie (authorization token)，如果不存在或发生错误则返回null。
     */
    public static String readYouPinCookie(String steamId) {
        if (steamId == null || steamId.trim().isEmpty()) {
            System.err.println("Steam ID 不能为空，无法读取YouPin Cookie。");
            return null;
        }

        String cookie = SqliteManager.queryYouPinCookie(steamId);
        if (cookie != null) {
            System.out.println("成功从数据库读取到YouPin Cookie for steamId '" + steamId + "'.");
        } else {
            System.out.println("未能在数据库中找到 steamId '" + steamId + "' 的YouPin Cookie，或读取失败。");
        }
        return cookie;
    }

    /**
     * 从SQLite数据库删除指定 steamId 的YouPin Cookie。
     * @param steamId Steam用户的唯一标识符。
     * @return 是否成功删除 (如果记录存在并被删除则为true)。
     */
    public static boolean deleteYouPinCookie(String steamId) {
        if (steamId == null || steamId.trim().isEmpty()) {
            System.err.println("Steam ID 不能为空，无法删除YouPin Cookie。");
            return false;
        }

        boolean success = SqliteManager.deleteYouPinCookie(steamId);
        if (success) {
            System.out.println("已成功从数据库删除 steamId '" + steamId + "' 的YouPin Cookie。");
        } else {
            System.out.println("数据库中不存在 steamId '" + steamId + "' 的YouPin Cookie，或删除失败。");
        }
        return success;
    }

    /**
     * 检查指定 steamId 的YouPin Cookie是否存在于数据库中，并验证其有效性。
     * @param steamId Steam用户的唯一标识符。
     * @return 如果YouPin Cookie存在且有效返回true，否则返回false。
     */
    public static boolean youPinCookieExists(String steamId) throws IOException {
        if (steamId == null || steamId.trim().isEmpty()) {
            System.err.println("Steam ID 不能为空，无法检查YouPin Cookie是否存在。");
            return false;
        }

        try {
            // 先从数据库中读取对应steamId的cookie
            String youPinToken = readYouPinCookie(steamId);
            if (youPinToken == null || youPinToken.trim().isEmpty()) {
                System.out.println("steamId '" + steamId + "' 的YouPin Cookie不存在于数据库中");
                return false;
            }

            // 使用cookie调用getUserInfo接口验证有效性
            YouPinServiceImpl youPinService = new YouPinServiceImpl();
            boolean effective = youPinService.getUserInfo(youPinToken);
            System.out.println("检查 steamId '" + steamId + "' 的YouPin Cookie是否有效: " + (effective ? "有效" : "过期"));
            return effective;
        } catch (Exception e) {
            System.err.println("检查 steamId '" + steamId + "' 的YouPin Cookie时发生异常: " + e.getMessage());
            return false;
        }
    }
}