package com.io661.extension.service.Impl;

import com.io661.extension.model.Enum.PlatEnum;
import com.io661.extension.model.User.PersonalSettings;
import com.io661.extension.service.PersonalSettingService;
import com.io661.extension.util.SQLite.SqliteManager;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PersonalSettingServiceImpl implements PersonalSettingService {

    /**
     * @param  platEnums 平台代码
     * @param  ratio 比例
     */
    @Override
    public List<PlatEnum> setPlatRation(List<PlatEnum> platEnums, int ratio) {
        // 保留原有方法，暂时返回null
        return null;
    }

    @Override
    public PersonalSettings getPersonalSettings() {
        return SqliteManager.getPersonalSettings();
    }

    @Override
    public boolean updatePersonalSettings(PersonalSettings settings) {
        if (settings == null) {
            System.err.println("个人设置对象不能为空");
            return false;
        }

        if (!settings.isValid()) {
            System.err.println("个人设置费率必须在0-1之间");
            return false;
        }

        return SqliteManager.updatePersonalSettings(settings);
    }

    @Override
    public boolean resetPersonalSettings() {
        return SqliteManager.resetPersonalSettings();
    }

    @Override
    public Map<String, Double> getPlatformFeeRates() {
        PersonalSettings settings = getPersonalSettings();
        Map<String, Double> feeRates = new HashMap<>();

        feeRates.put("io661", settings.getIo661TransRatio());
        feeRates.put("youpin", settings.getYouPinTransRatio());
        feeRates.put("buff", 0.025); // BUFF平台暂时使用固定值

        return feeRates;
    }

    @Override
    public boolean setPlatformFeeRate(String platform, double feeRate) {
        if (platform == null || platform.trim().isEmpty()) {
            System.err.println("平台名称不能为空");
            return false;
        }

        if (feeRate < 0 || feeRate > 1) {
            System.err.println("费率必须在0-1之间");
            return false;
        }

        try {
            PersonalSettings settings = getPersonalSettings();

            switch (platform.toLowerCase()) {
                case "io661":
                    settings.setIo661TransRatio(feeRate);
                    break;
                case "youpin":
                    settings.setYouPinTransRatio(feeRate);
                    break;
                default:
                    System.err.println("不支持的平台: " + platform);
                    return false;
            }

            boolean success = updatePersonalSettings(settings);
            if (success) {
                System.out.println("平台 " + platform + " 费率设置为 " + (feeRate * 100) + "%");
            }
            return success;
        } catch (Exception e) {
            System.err.println("设置平台费率失败: " + e.getMessage());
            return false;
        }
    }

    @Override
    public int calculateListingPrice(String platform, int targetPrice) {
        if (targetPrice <= 0) {
            return 0;
        }

        Double feeRate = getPlatformFeeRates().get(platform.toLowerCase());
        if (feeRate == null) {
            System.err.println("未知平台: " + platform + "，使用默认费率5%");
            feeRate = 0.05;
        }

        // 计算公式: 上架价格 = 到手价 / (1 - 费率)
        double listingPrice = targetPrice / (1.0 - feeRate);

        // 向上取整，确保到手价不少于目标价格
        return (int) Math.ceil(listingPrice);
    }

    @Override
    public int calculateReceivedPrice(String platform, int listingPrice) {
        if (listingPrice <= 0) {
            return 0;
        }

        Double feeRate = getPlatformFeeRates().get(platform.toLowerCase());
        if (feeRate == null) {
            System.err.println("未知平台: " + platform + "，使用默认费率5%");
            feeRate = 0.05;
        }

        // 计算公式: 到手价 = 上架价格 * (1 - 费率)
        double receivedPrice = listingPrice * (1.0 - feeRate);

        // 向下取整
        return (int) Math.floor(receivedPrice);
    }
}
