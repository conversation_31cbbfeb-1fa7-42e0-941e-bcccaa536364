package com.io661.extension.controller;

import com.io661.extension.model.User.PersonalSettings;
import com.io661.extension.service.Impl.PersonalSettingServiceImpl;
import com.io661.extension.service.PersonalSettingService;
import javafx.application.Platform;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.layout.VBox;

import java.net.URL;
import java.text.DecimalFormat;
import java.util.ResourceBundle;

public class PersonalSettingController implements Initializable {

    @FXML
    private TextField io661TransRateField;

    @FXML
    private TextField io661WithdrawRateField;

//    @FXML
//    private TextField youPinTransRateField;

//    @FXML
//    private TextField youPinWithdrawRateField;

    @FXML
    private Button saveButton;

    @FXML
    private Button resetButton;

    @FXML
    private Label statusLabel;

    @FXML
    private VBox testCalculationContainer;

    @FXML
    private TextField testReceivedPriceField;

    @FXML
    private ComboBox<String> testPlatformComboBox;

    @FXML
    private Label testListingPriceLabel;

    private PersonalSettingService personalSettingService;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        personalSettingService = new PersonalSettingServiceImpl();

        // 初始化平台选择器
//        testPlatformComboBox.getItems().addAll("io661", "youpin", "buff");
        testPlatformComboBox.getItems().addAll("io661");
        testPlatformComboBox.setValue("io661");

        // 加载当前费率配置
        loadCurrentFeeRates();

        // 设置事件监听
        setupEventListeners();

        // 初始化测试计算
        updateTestCalculation();
    }

    /**
     * 加载当前费率配置
     */
    private void loadCurrentFeeRates() {
        PersonalSettings settings = personalSettingService.getPersonalSettings();

        DecimalFormat df = new DecimalFormat("0.00");
        io661TransRateField.setText(df.format(settings.getIo661TransRatio() * 100));
        io661WithdrawRateField.setText(df.format(settings.getIo661WithdrawRatio() * 100));
//        youPinTransRateField.setText(df.format(settings.getYouPinTransRatio() * 100));
//        youPinWithdrawRateField.setText(df.format(settings.getYouPinWithdrawRatio() * 100));
    }

    /**
     * 设置事件监听器
     */
    private void setupEventListeners() {
        // 费率输入框格式限制
        setupFeeRateField(io661TransRateField);
        setupFeeRateField(io661WithdrawRateField);
//        setupFeeRateField(youPinTransRateField);
//        setupFeeRateField(youPinWithdrawRateField);

        // 测试计算事件
        testReceivedPriceField.textProperty().addListener((observable, oldValue, newValue) -> updateTestCalculation());
        testPlatformComboBox.valueProperty().addListener((observable, oldValue, newValue) -> updateTestCalculation());
    }

    /**
     * 设置费率输入框
     */
    private void setupFeeRateField(TextField field) {
        // 限制输入格式：只允许数字和小数点，最多两位小数
        field.textProperty().addListener((observable, oldValue, newValue) -> {
            if (!newValue.matches("\\d*(\\.\\d{0,2})?")) {
                field.setText(oldValue);
            }
        });

        // 焦点事件
        field.focusedProperty().addListener((observable, oldValue, newValue) -> {
            if (newValue) {
                Platform.runLater(field::selectAll);
            } else {
                // 失去焦点时验证范围
                try {
                    if (!field.getText().isEmpty()) {
                        double value = Double.parseDouble(field.getText());
                        if (value < 0 || value > 100) {
                            showAlert(Alert.AlertType.WARNING, "费率范围错误", "费率必须在0-100之间");
                            field.setText("5.00"); // 设置默认值
                        }
                    }
                } catch (NumberFormatException e) {
                    field.setText("5.00"); // 设置默认值
                }
            }
        });
    }

    /**
     * 更新测试计算结果
     */
    private void updateTestCalculation() {
        try {
            String receivedPriceText = testReceivedPriceField.getText();
            String platform = testPlatformComboBox.getValue();

            if (receivedPriceText != null && !receivedPriceText.isEmpty() && platform != null) {
                double receivedPrice = Double.parseDouble(receivedPriceText);
                if (receivedPrice > 0) {
                    int receivedPriceInCents = (int) (receivedPrice * 100);
                    int listingPriceInCents = personalSettingService.calculateListingPrice(platform, receivedPriceInCents);
                    double listingPrice = listingPriceInCents / 100.0;

                    DecimalFormat df = new DecimalFormat("0.00");
                    testListingPriceLabel.setText("上架价格: ¥" + df.format(listingPrice));
                    testListingPriceLabel.setStyle("-fx-text-fill: #2196F3;");
                } else {
                    testListingPriceLabel.setText("上架价格: --");
                    testListingPriceLabel.setStyle("-fx-text-fill: #666666;");
                }
            } else {
                testListingPriceLabel.setText("上架价格: --");
                testListingPriceLabel.setStyle("-fx-text-fill: #666666;");
            }
        } catch (NumberFormatException e) {
            testListingPriceLabel.setText("上架价格: --");
            testListingPriceLabel.setStyle("-fx-text-fill: #666666;");
        }
    }

    /**
     * 保存费率配置
     */
    @FXML
    private void handleSave() {
        try {
            // 验证输入
            double io661TransRate = Double.parseDouble(io661TransRateField.getText()) / 100.0;
            double io661WithdrawRate = Double.parseDouble(io661WithdrawRateField.getText()) / 100.0;
//            double youPinTransRate = Double.parseDouble(youPinTransRateField.getText()) / 100.0;
//            double youPinWithdrawRate = Double.parseDouble(youPinWithdrawRateField.getText()) / 100.0;

            // 创建个人设置对象
            PersonalSettings settings = new PersonalSettings();
            settings.setIo661TransRatio(io661TransRate);
            settings.setIo661WithdrawRatio(io661WithdrawRate);
//            settings.setYouPinTransRatio(youPinTransRate);
//            settings.setYouPinWithdrawRatio(youPinWithdrawRate);

            // 验证范围
            if (!settings.isValid()) {
                showAlert(Alert.AlertType.ERROR, "费率错误", "费率必须在0-100之间");
                return;
            }

            // 保存配置
            boolean success = personalSettingService.updatePersonalSettings(settings);

            if (success) {
                statusLabel.setText("费率配置保存成功");
                statusLabel.setStyle("-fx-text-fill: #4CAF50;");
                showAlert(Alert.AlertType.INFORMATION, "保存成功", "平台费率配置已保存");
            } else {
                statusLabel.setText("费率配置保存失败");
                statusLabel.setStyle("-fx-text-fill: #F44336;");
                showAlert(Alert.AlertType.ERROR, "保存失败", "保存费率配置时发生错误");
            }

            // 更新测试计算
            updateTestCalculation();

        } catch (NumberFormatException e) {
            showAlert(Alert.AlertType.ERROR, "输入错误", "请输入有效的费率数值");
        }
    }

    /**
     * 重置为默认费率
     */
    @FXML
    private void handleReset() {
        Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
        confirmAlert.setTitle("确认重置");
        confirmAlert.setHeaderText(null);
        confirmAlert.setContentText("确定要重置为默认费率配置吗？");

        confirmAlert.showAndWait().ifPresent(response -> {
            if (response == ButtonType.OK) {
                // 重置数据库中的设置
                boolean success = personalSettingService.resetPersonalSettings();

                if (success) {
                    // 重新加载界面显示
                    loadCurrentFeeRates();

                    statusLabel.setText("已重置为默认费率");
                    statusLabel.setStyle("-fx-text-fill: #FF9800;");

                    // 更新测试计算
                    updateTestCalculation();
                } else {
                    statusLabel.setText("重置失败");
                    statusLabel.setStyle("-fx-text-fill: #F44336;");
                    showAlert(Alert.AlertType.ERROR, "重置失败", "重置费率配置时发生错误");
                }
            }
        });
    }

    /**
     * 显示提示框
     */
    private void showAlert(Alert.AlertType alertType, String title, String content) {
        Alert alert = new Alert(alertType);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(content);
        alert.showAndWait();
    }
}