<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.VBox?>
<?import javafx.geometry.Insets?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.shape.Line?>
<?import java.net.URL?>

<AnchorPane prefHeight="300.0" prefWidth="250.0" styleClass="user-info-card"
            xmlns="http://javafx.com/javafx/21"
            xmlns:fx="http://javafx.com/fxml/1"
            fx:controller="com.io661.extension.controller.UserController">
    <stylesheets>
        <URL value="@../../css/user-info-card.css" />
    </stylesheets>
    <VBox spacing="15" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0" AnchorPane.bottomAnchor="0.0">
        <!-- 用户信息部分 -->
        <VBox spacing="10" styleClass="user-info-section" alignment="CENTER">
            <padding>
                <Insets top="20" right="20" bottom="15" left="20"/>
            </padding>
            <!-- 用户头像 -->
            <ImageView fx:id="userAvatar" fitHeight="80.0" fitWidth="80.0" pickOnBounds="true" preserveRatio="true" styleClass="user-avatar"/>
            <!-- 用户昵称 -->
            <Label fx:id="userNickname" styleClass="user-nickname" text="用户昵称"/>
            <!-- 余额和提现按钮 -->
            <HBox spacing="10" alignment="CENTER">
                <Label fx:id="userBalance" styleClass="user-balance" text="0.00 ¥"/>
                <Button fx:id="withdrawButton" styleClass="withdraw-button" text="提现"/>
            </HBox>
        </VBox>

        <!-- 分割线 -->
        <Line startX="0" endX="210" stroke="#e8e8e8" strokeWidth="1"/>

        <!-- 功能按钮部分 -->
        <VBox spacing="8" styleClass="function-section">
            <padding>
                <Insets top="5" right="20" bottom="20" left="20"/>
            </padding>
            <Button fx:id="editProfileButton" styleClass="menu-button" text="📝 编辑资料" maxWidth="Infinity"/>
            <Button fx:id="logoutButton" styleClass="menu-button" text="🚪 退出登录" maxWidth="Infinity"/>
        </VBox>
    </VBox>
</AnchorPane>
