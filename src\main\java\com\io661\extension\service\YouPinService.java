package com.io661.extension.service;

import com.io661.extension.model.YouPin.*;

import java.io.IOException;
import java.util.Map;

public interface YouPinService {
    /**
     * 发送登录短信
     * @param phone 手机号
     * @return 返回是否发送成功
     */
    YouPinSendSmsRes sendSmsCode(String phone) throws IOException;

    /**
     * 登录账号
     * @param phone 手机号
     * @param code  验证码
     * @return 成功返回授权令牌，失败返回null
     */
    boolean loginAccount(String phone, String code) throws IOException;

    /**
     * 获取用户信息
     *
     * @param token 授权令牌
     * @return 用户信息Map
     */
    boolean getUserInfo(String token) throws IOException;
    /**
     * 获取用户YouPin库存
     * @param token 授权令牌
     * @return 用户库存Map
     */
    Map<String, Object> getUserInventoryDataList(String token) throws IOException;

    /**
     * 获取用户YouPin在售
     * @param token 授权令牌
     * @return 用户在售Map
     */
    YouPinUserInventoryOnSellDataListRes getUserInventoryOnSellDataList(String token) throws IOException;

    /**
     * 获取用户YouPin待发货
     * @param token 授权令牌
     * @return 用户在售Map
     */
    Map<String, Object> getUserWaitDeliverList(String token) throws IOException;

    /**
     * 用户YouPin上架饰品
     * @param token 授权令牌
     * @return 上架成功失败
     */
    YouPinUserSellInventoryRes userSellInventory(String token, YouPinUserSellInventoryReq youPinUserSellInventoryReq) throws IOException;

    /**
     * 用户YouPin改价饰品
     * @param token 授权令牌
     * @param request 改价请求
     * @return 改价响应
     */
    YouPinUserItemsOnSellPriceChangeRes userItemsOnSellPriceChange(String token, YouPinUserItemsOnSellPriceChangeReq request) throws IOException;
    /**
     * 用户YouPin改价饰品
     * @param token 授权令牌
     * @return 上架成功失败
     */
    boolean userItemsOffSale(String token, YouPinUserItemsOffSaleReq youPinUserItemsOffSaleReq) throws IOException;

    /**
     * 获取商品信息
     * @param token 授权令牌
     * @return 商品信息数组
     */
    Object[] getGoodsInfo(String token) throws IOException;

    /**
     * 发送校验验证码
     * @param userId 用户ID
     * @param token 授权令牌
     * @return 返回结果数组，包含[状态码, 消息, 会话ID]
     */
    String[] sendCode(String userId, String token) throws IOException;


    /**
     * 确认支付商品
     * @param orderNo 订单号
     * @param waitPaymentDataNo 付订单号
     * @param goodsPrice 商品价格
     * @param token 授权令牌
     * @return 返回结果数组，包含[状态码, 消息]
     */
    String[] confirmOrder(String orderNo, String payOrderNo, String waitPaymentDataNo,int goodsPrice, String token)throws IOException;

    /**
     * 校验验证码并创建订单
     * @param sessionId 会话ID
     * @param code 验证码
     * @param token 授权令牌
     * @return 返回结果数组，包含[状态码, 消息]
     */
    String[] checkCode(String sessionId, String code, String token) throws IOException;


}
