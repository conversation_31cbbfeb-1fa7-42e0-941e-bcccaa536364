package com.io661.extension.model.User;

import lombok.Data;

@Data
public class UserInfoRes {
    private int code;
    private Data_ data;

    @Data
    public static class Data_ {
        /**
         * 编号
         */
        private Long id;
        /**
         * 昵称
         */
        private String nickname;
        /**
         * 头像
         */
        private String avatar;
        /**
         * 手机号
         */
        private String phone;
        /**
         * 余额（分）
         */
        private Integer balance;
        /**
         * 用户真名（脱敏）
         */
        private String name;
        /**
         * 可提现余额
         */
        private Integer withdrawBalance;
        /**
         * 提现手续费率
         */
        private Float withdrawRate;
        /**
         * 上次提现手机号
         */
        private String withdrawAccount;

    }
}
