package com.io661.extension.commonURL;


import lombok.Getter;
import lombok.Setter;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import javax.net.ssl.HttpsURLConnection;
import com.io661.extension.util.SSLCertificateManager;

public class CommonHttpUrl {
    // 配置URL - 可以根据需要切换HTTP/HTTPS
    private final String BASE_URL = "http://io661.com/";  // HTTP版本（推荐用于当前环境）
    // private final String BASE_URL = "https://io661.com/";  // HTTPS版本（需要正确的SSL证书配置）
    // private final String BASE_URL = "http://192.168.0.110:8661/";  // 开发环境URL

    /**
     * -- SETTER --
     *  设置授权令牌
     * -- GETTER --
     *  获取授权令牌
     */
    @Getter
    @Setter
    private String authToken; // 存储授权令牌

    /**
     * 发送GET请求
     *
     * @param endpoint 接口路径
     * @param params 请求参数
     * @param headers 请求头
     * @return 响应结果字符串
     * @throws IOException 请求异常
     */
    public String doGet(String endpoint, Map<String, String> params, Map<String, String> headers) throws IOException {
        StringBuilder urlBuilder = new StringBuilder(BASE_URL + endpoint);

        // 添加请求参数
        if (params != null && !params.isEmpty()) {
            urlBuilder.append("?");
            for (Map.Entry<String, String> entry : params.entrySet()) {
                urlBuilder.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
            }
            urlBuilder.deleteCharAt(urlBuilder.length() - 1); // 删除最后一个&
        }

        URL url = URI.create(urlBuilder.toString()).toURL();
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        // 只对HTTPS连接配置SSL证书
        if (connection instanceof HttpsURLConnection && url.getProtocol().equals("https")) {
            try {
                SSLCertificateManager.initializeSSLContext();
                HttpsURLConnection httpsConnection = (HttpsURLConnection) connection;
                httpsConnection.setSSLSocketFactory(SSLCertificateManager.getSSLSocketFactory());
                httpsConnection.setHostnameVerifier(SSLCertificateManager.getHostnameVerifier());
                System.out.println("已为HTTPS连接配置SSL: " + url);
            } catch (Exception e) {
                System.err.println("SSL配置失败，使用默认设置: " + e.getMessage());
            }
        }

        connection.setRequestMethod("GET");

        // 如果headers为null，创建一个新的Map
        if (headers == null) {
            headers = new HashMap<>();
        }

        // 添加Authorization Cookie
        addAuthorizationCookie(headers, authToken);

        // 添加请求头
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            connection.setRequestProperty(entry.getKey(), entry.getValue());
        }

        return getResponse(connection);
    }

    /**
     * 发送POST请求
     *
     * @param endpoint 接口路径
     * @param jsonBody JSON格式的请求体
     * @param headers 请求头
     * @return 响应结果字符串
     * @throws IOException 请求异常
     */
    public String doPost(String endpoint, String jsonBody, Map<String, String> headers) throws IOException {
        URL url = URI.create(BASE_URL + endpoint).toURL();
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        // 只对HTTPS连接配置SSL证书
        if (connection instanceof HttpsURLConnection && url.getProtocol().equals("https")) {
            try {
                SSLCertificateManager.initializeSSLContext();
                HttpsURLConnection httpsConnection = (HttpsURLConnection) connection;
                httpsConnection.setSSLSocketFactory(SSLCertificateManager.getSSLSocketFactory());
                httpsConnection.setHostnameVerifier(SSLCertificateManager.getHostnameVerifier());
                System.out.println("已为HTTPS连接配置SSL: " + url);
            } catch (Exception e) {
                System.err.println("SSL配置失败，使用默认设置: " + e.getMessage());
            }
        }

        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setDoOutput(true);

        // 如果headers为null，创建一个新的Map
        if (headers == null) {
            headers = new HashMap<>();
        }

        // 添加Authorization Cookie
        addAuthorizationCookie(headers, authToken);

        // 添加请求头
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            connection.setRequestProperty(entry.getKey(), entry.getValue());
        }

        // 写入请求体
        if (jsonBody != null && !jsonBody.isEmpty()) {
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = jsonBody.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            } catch (IOException e) {
                System.err.println("写入请求体时发生错误: " + e.getMessage());
                throw e; // 重新抛出异常，让上层处理
            }
        }

        return getResponse(connection);
    }

    /**
     * 发送PUT请求
     *
     * @param endpoint 接口路径
     * @param jsonBody JSON格式的请求体
     * @param headers 请求头
     * @return 响应结果字符串
     * @throws IOException 请求异常
     */
    public String doPut(String endpoint, String jsonBody, Map<String, String> headers) throws IOException {
        URL url = URI.create(BASE_URL + endpoint).toURL();
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        // 只对HTTPS连接配置SSL证书
        if (connection instanceof HttpsURLConnection && url.getProtocol().equals("https")) {
            try {
                SSLCertificateManager.initializeSSLContext();
                HttpsURLConnection httpsConnection = (HttpsURLConnection) connection;
                httpsConnection.setSSLSocketFactory(SSLCertificateManager.getSSLSocketFactory());
                httpsConnection.setHostnameVerifier(SSLCertificateManager.getHostnameVerifier());
                System.out.println("已为HTTPS连接配置SSL: " + url);
            } catch (Exception e) {
                System.err.println("SSL配置失败，使用默认设置: " + e.getMessage());
            }
        }

        connection.setRequestMethod("PUT");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setDoOutput(true);

        // 如果headers为null，创建一个新的Map
        if (headers == null) {
            headers = new HashMap<>();
        }

        // 如果authToken不为空，添加Authorization作为Cookie
        if (authToken != null && !authToken.isEmpty() && !headers.containsKey("Cookie")) {
            // 将Authorization作为Cookie发送，与Apifox保持一致
            headers.put("Cookie", "Authorization=" + authToken);
        }

        // 添加请求头
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            connection.setRequestProperty(entry.getKey(), entry.getValue());
        }

        // 写入请求体
        if (jsonBody != null && !jsonBody.isEmpty()) {
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = jsonBody.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }
        }

        return getResponse(connection);
    }

    /**
     * 发送DELETE请求
     *
     * @param endpoint 接口路径
     * @param headers 请求头
     * @return 响应结果字符串
     * @throws IOException 请求异常
     */
    public String doDelete(String endpoint, Map<String, String> headers) throws IOException {
        URL url = URI.create(BASE_URL + endpoint).toURL();
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        // 只对HTTPS连接配置SSL证书
        if (connection instanceof HttpsURLConnection && url.getProtocol().equals("https")) {
            try {
                SSLCertificateManager.initializeSSLContext();
                HttpsURLConnection httpsConnection = (HttpsURLConnection) connection;
                httpsConnection.setSSLSocketFactory(SSLCertificateManager.getSSLSocketFactory());
                httpsConnection.setHostnameVerifier(SSLCertificateManager.getHostnameVerifier());
                System.out.println("已为HTTPS连接配置SSL: " + url);
            } catch (Exception e) {
                System.err.println("SSL配置失败，使用默认设置: " + e.getMessage());
            }
        }

        connection.setRequestMethod("DELETE");

        // 如果headers为null，创建一个新的Map
        if (headers == null) {
            headers = new HashMap<>();
        }

        // 添加Authorization Cookie
        addAuthorizationCookie(headers, authToken);

        // 添加请求头
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            connection.setRequestProperty(entry.getKey(), entry.getValue());
        }

        return getResponse(connection);
    }

    /**
     * 获取响应结果
     *
     * @param connection HTTP连接
     * @return 响应结果字符串
     * @throws IOException 请求异常
     */
    private String getResponse(HttpURLConnection connection) throws IOException {
        int responseCode = connection.getResponseCode();
        StringBuilder response = new StringBuilder();

        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(
                        responseCode >= 400 ? connection.getErrorStream() : connection.getInputStream(),
                        StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
        }

        return response.toString();
    }

    /**
     * 获取基础URL
     *
     * @return 基础URL
     */
    public String getBaseUrl() {
        return BASE_URL;
    }

    /**
     * 添加Authorization Cookie到请求头中
     *
     * @param headers 请求头Map
     * @param token 授权令牌
     */
    private void addAuthorizationCookie(Map<String, String> headers, String token) {
        if (token != null && !token.isEmpty() && !headers.containsKey("Cookie")) {
            headers.put("Cookie", "Authorization=" + token);
        }
    }

    /**
     * 使用保存的令牌发送请求
     *
     * @param endpoint 接口路径
     * @param method 请求方法（GET, POST, PUT, DELETE）
     * @param jsonBody JSON格式的请求体（仅用于POST和PUT请求）
     * @return 响应结果字符串
     * @throws IOException 请求异常
     */
    public String sendRequestWithToken(String endpoint, String method, String jsonBody) throws IOException {
        Map<String, String> headers = new HashMap<>();

        // 添加Authorization Cookie
        addAuthorizationCookie(headers, authToken);

        return switch (method.toUpperCase()) {
            case "GET" -> doGet(endpoint, null, headers);
            case "POST" -> doPost(endpoint, jsonBody, headers);
            case "PUT" -> doPut(endpoint, jsonBody, headers);
            case "DELETE" -> doDelete(endpoint, headers);
            default -> throw new IllegalArgumentException("不支持的HTTP方法: " + method);
        };
    }
}
