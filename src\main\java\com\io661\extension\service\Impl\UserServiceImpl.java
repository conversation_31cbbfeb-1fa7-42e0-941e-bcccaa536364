package com.io661.extension.service.Impl;

import com.google.gson.Gson;
import com.io661.extension.commonURL.CommonHttpUrl;
import com.io661.extension.model.User.EditUserInfoReq;
import com.io661.extension.model.User.EditUserInfoRes;
import com.io661.extension.model.User.UserInfoRes;
import com.io661.extension.service.UserService;
import lombok.Data;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Data
public class UserServiceImpl implements UserService {
    private final CommonHttpUrl httpClient;

    public UserServiceImpl() {
        this.httpClient = new CommonHttpUrl();
    }

    @Override
    public boolean validateToken(String token) {
        if (token == null || token.trim().isEmpty()) {
            System.out.println("令牌为空，无法验证");
            return false;
        }
        try {
            System.out.println("正在验证令牌: " + token.substring(0, Math.min(20, token.length())) + "...");
            UserInfoRes.Data_ userInfo = getUserInfo(token);

            // 检查响应是否包含成功代码
            boolean isValid = userInfo != null && userInfo.getId() != null;
            System.out.println("令牌验证结果: " + (isValid ? "有效" : "无效"));

            return isValid;
        } catch (Exception e) {
            System.err.println("验证令牌失败: " + e.getMessage());
            return false;
        }
    }

    @Override
    public UserInfoRes.Data_ getUserInfo(String token) {
        UserInfoRes.Data_ result = new UserInfoRes.Data_();

        if (token == null || token.trim().isEmpty()) {
            System.out.println("令牌为空，无法获取用户信息");
            return null;
        }
        try {
            // 临时设置授权令牌
            httpClient.setAuthToken(token);

            // 创建请求头
            Map<String, String> headers = new HashMap<>();

            headers.put("Cookie", "Authorization=" + token);

            // 发送GET请求到用户信息接口
            String endpoint = "web/user";

            // 直接使用doGet方法，确保请求头被正确设置
            String response = httpClient.doGet(endpoint, null, headers);
            Gson gson = new Gson();

            UserInfoRes userInfoRes = gson.fromJson(response, UserInfoRes.class);

            if (userInfoRes != null && userInfoRes.getData().getId() != null) {
                result.setId(userInfoRes.getData().getId());
                result.setAvatar(userInfoRes.getData().getAvatar());
                result.setNickname(userInfoRes.getData().getNickname());
                result.setName(userInfoRes.getData().getName());
                result.setPhone(userInfoRes.getData().getPhone());
                result.setBalance(userInfoRes.getData().getBalance());
                result.setWithdrawAccount(userInfoRes.getData().getWithdrawAccount());
                result.setWithdrawBalance(userInfoRes.getData().getWithdrawBalance());
                result.setWithdrawRate(userInfoRes.getData().getWithdrawRate());
                return result;
            }
            return result;
        } catch (IOException e) {
            System.err.println("获取用户信息失败: " + e.getMessage());
            return null;
        } finally {
            // 清除临时设置的授权令牌
            httpClient.setAuthToken(null);
        }
    }

    @Override
    public boolean editUserInfo(EditUserInfoReq request, String token) {

        if (token == null || token.trim().isEmpty()) {
            System.out.println("令牌为空，无法获取用户信息");
            return false;
        }
        try {
            // 临时设置授权令牌
            httpClient.setAuthToken(token);

            // 创建请求头
            Map<String, String> headers = new HashMap<>();

            headers.put("Cookie", "Authorization=" + token);

            // 将请求对象转换为JSON
            Gson gson = new Gson();
            String jsonBody = gson.toJson(request);

            System.out.println(jsonBody);

            // 发送GET请求到用户信息接口
            String endpoint = "web/user";

            String response = httpClient.doPut(endpoint, jsonBody, headers);

            EditUserInfoRes userInfoRes = gson.fromJson(response, EditUserInfoRes.class);
            return userInfoRes.getCode() == 0;
        } catch (IOException e) {
            return false;
        }
    }

    @Override
    public boolean logout(String token){
        if (token == null || token.trim().isEmpty()) {
            System.out.println("令牌为空，无法获取用户信息");
            return false;
        }
        try {
            // 临时设置授权令牌
            httpClient.setAuthToken(token);

            // 创建请求头
            Map<String, String> headers = new HashMap<>();

            headers.put("Cookie", "Authorization=" + token);

            // 发送GET请求到用户信息接口
            String endpoint = "web/user";

            String response = httpClient.doDelete(endpoint,  headers);
            Gson gson = new Gson();
            EditUserInfoRes userInfoRes = gson.fromJson(response, EditUserInfoRes.class);

            return userInfoRes.getCode() == 0;
        } catch (IOException e) {
            return true;
        }
    }
}
