/* 登录面板样式 */
.login-pane {
    -fx-background-color: white;
    -fx-border-color: rgba(40, 215, 222, 0.93);
    -fx-border-width: 1px;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.3), 10, 0, 0, 0);
    -fx-background-radius: 5px;
    -fx-border-radius: 5px;
}

/* 标题样式 */
.login-title {
    -fx-font-size: 20px;
    -fx-font-weight: bold;
    -fx-text-fill: #333333;
    -fx-fill: #333333;
}

/* 输入框通用样式 */
.input-field {
    -fx-background-color: white;
    -fx-background-radius: 3px;
    -fx-border-radius: 3px;
    -fx-border-color: #cccccc;
    -fx-border-width: 1px;
    -fx-padding: 8px;
    -fx-font-size: 13px;
    -fx-prompt-text-fill: #999999;
}

/* 验证码输入框特殊样式 */
#codeText {
    -fx-padding: 8px 85px 8px 8px; /* 右侧留出空间给按钮 */
}

.input-field:focused {
    -fx-border-color: #43c3dc;
}

/* 验证码按钮样式 - 透明版 */
.code-button-transparent {
    -fx-background-color: transparent;
    -fx-text-fill: #5EDFD6;
    -fx-border-color: transparent;
    -fx-border-width: 0;
    -fx-cursor: hand;
    -fx-font-size: 13px;
    -fx-font-weight: normal;
    -fx-padding: 0 5px;
}

.code-button-transparent:hover {
    -fx-text-fill: #37D4CF;
}

.code-button-transparent:pressed {
    -fx-text-fill: #0DA5AA;
}

.code-button-transparent:disabled {
    -fx-text-fill: #999999;
    -fx-opacity: 1.0;
}

/* 登录按钮样式 */
.login-button {
    -fx-background-color: #5EDFD6;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-background-radius: 5px;
    -fx-cursor: hand;
    -fx-font-size: 14px;
}

.login-button:hover {
    -fx-background-color: #37D4CF;
}

.login-button:pressed {
    -fx-background-color: #0DA5AA;
}

/* 记住登录复选框样式 */
.remember-checkbox {
    -fx-text-fill: #666666;
    -fx-font-size: 12px;
}

.remember-checkbox .box {
    -fx-background-color: white;
    -fx-border-color: #cccccc;
    -fx-border-width: 1px;
}

.remember-checkbox:selected .box {
    -fx-background-color: #43c3dc;
}

.remember-checkbox:selected .mark {
    -fx-background-color: white;
}

/* 协议容器样式 */
.agreement-container {
    -fx-spacing: 2;
    -fx-alignment: center;
}

/* 协议复选框样式 */
.agreement-checkbox {
    -fx-opacity: 1.0;
}

.agreement-checkbox .box {
    -fx-background-color: white;
    -fx-border-color: #cccccc;
    -fx-border-width: 1px;
}

.agreement-checkbox:selected .box {
    -fx-background-color: #43c3dc;
}

.agreement-checkbox:selected .mark {
    -fx-background-color: white;
}

/* 协议文本样式 */
.agreement-text {
    -fx-text-fill: #666666;
    -fx-font-size: 12px;
}

/* 协议链接样式 */
.agreement-link {
    -fx-border-color: transparent;
    -fx-padding: 0;
    -fx-text-fill: #43c3dc;
    -fx-font-size: 12px;
    -fx-underline: false;
}

.agreement-link:hover {
    -fx-underline: true;
}

/* 容器样式 */
HBox {
    -fx-alignment: center;
}

VBox {
    -fx-alignment: top-center;
}

.login-bg {
    position: relative;
    box-sizing: border-box;
    -fx-background-color:
            linear-gradient(to bottom, transparent 20%, #ffffff 100%),
            radial-gradient(center 0% 0%, radius 70%, #5EDFD6, transparent),
            radial-gradient(center 100% 0%, radius 40%, #a0e3fb, transparent);
    background-size: cover;
    background-position: center;
}
