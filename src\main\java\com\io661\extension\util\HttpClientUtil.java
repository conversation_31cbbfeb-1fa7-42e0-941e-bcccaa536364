package com.io661.extension.util;

import javax.net.ssl.HttpsURLConnection;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * HTTP客户端工具类
 * 支持动态URL和全局SSL配置
 */
public class HttpClientUtil {
    
    /**
     * 发送GET请求到指定URL
     * @param url 完整的URL
     * @param headers 请求头
     * @return 响应结果
     * @throws IOException 请求异常
     */
    public static String doGet(String url, Map<String, String> headers) throws IOException {
        return sendRequest(url, "GET", null, headers);
    }
    
    /**
     * 发送POST请求到指定URL
     * @param url 完整的URL
     * @param jsonBody JSON请求体
     * @param headers 请求头
     * @return 响应结果
     * @throws IOException 请求异常
     */
    public static String doPost(String url, String jsonBody, Map<String, String> headers) throws IOException {
        return sendRequest(url, "POST", jsonBody, headers);
    }
    
    /**
     * 发送PUT请求到指定URL
     * @param url 完整的URL
     * @param jsonBody JSON请求体
     * @param headers 请求头
     * @return 响应结果
     * @throws IOException 请求异常
     */
    public static String doPut(String url, String jsonBody, Map<String, String> headers) throws IOException {
        return sendRequest(url, "PUT", jsonBody, headers);
    }
    
    /**
     * 发送DELETE请求到指定URL
     * @param url 完整的URL
     * @param headers 请求头
     * @return 响应结果
     * @throws IOException 请求异常
     */
    public static String doDelete(String url, Map<String, String> headers) throws IOException {
        return sendRequest(url, "DELETE", null, headers);
    }
    
    /**
     * 通用HTTP请求方法
     * @param urlString 完整的URL
     * @param method HTTP方法
     * @param jsonBody JSON请求体（可选）
     * @param headers 请求头（可选）
     * @return 响应结果
     * @throws IOException 请求异常
     */
    private static String sendRequest(String urlString, String method, String jsonBody, Map<String, String> headers) throws IOException {
        URL url = URI.create(urlString).toURL();
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        
        // 如果是HTTPS连接且启用了全局SSL配置，则自动应用SSL设置
        if (connection instanceof HttpsURLConnection && url.getProtocol().equals("https")) {
            if (SSLCertificateManager.isGlobalSSLEnabled()) {
                System.out.println("使用全局SSL配置: " + url);
            } else {
                // 如果没有启用全局SSL，为这个连接单独配置SSL
                try {
                    SSLCertificateManager.initializeSSLContext();
                    HttpsURLConnection httpsConnection = (HttpsURLConnection) connection;
                    httpsConnection.setSSLSocketFactory(SSLCertificateManager.getSSLSocketFactory());
                    httpsConnection.setHostnameVerifier(SSLCertificateManager.getHostnameVerifier());
                    System.out.println("为单个连接配置SSL: " + url);
                } catch (Exception e) {
                    System.err.println("SSL配置失败，使用默认设置: " + e.getMessage());
                }
            }
        }
        
        // 设置请求方法
        connection.setRequestMethod(method);
        
        // 设置通用请求头
        connection.setRequestProperty("User-Agent", "IO661Extension/1.0");
        connection.setRequestProperty("Accept", "application/json, text/plain, */*");
        
        // 如果有请求体，设置Content-Type
        if (jsonBody != null && !jsonBody.isEmpty()) {
            connection.setRequestProperty("Content-Type", "application/json; charset=UTF-8");
            connection.setDoOutput(true);
        }
        
        // 添加自定义请求头
        if (headers != null) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                connection.setRequestProperty(entry.getKey(), entry.getValue());
            }
        }
        
        // 写入请求体
        if (jsonBody != null && !jsonBody.isEmpty()) {
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = jsonBody.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            } catch (IOException e) {
                System.err.println("写入请求体时发生错误: " + e.getMessage());
                throw e;
            }
        }
        
        // 获取响应
        return getResponse(connection);
    }
    
    /**
     * 获取HTTP响应
     * @param connection HTTP连接
     * @return 响应字符串
     * @throws IOException 读取异常
     */
    private static String getResponse(HttpURLConnection connection) throws IOException {
        int responseCode = connection.getResponseCode();
        StringBuilder response = new StringBuilder();
        
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(
                        responseCode >= 400 ? connection.getErrorStream() : connection.getInputStream(),
                        StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
        }
        
        return response.toString();
    }
    
    /**
     * 从响应数据中提取URL并发送请求
     * 用于处理响应数据中包含的动态HTTPS URL
     * @param responseData 包含URL的响应数据
     * @param urlPattern URL提取模式（正则表达式）
     * @param headers 请求头
     * @return URL对应的响应数据
     */
    public static String fetchFromResponseUrls(String responseData, String urlPattern, Map<String, String> headers) {
        // 这里可以实现URL提取和批量请求的逻辑
        // 例如：从JSON响应中提取图片URL、API URL等
        System.out.println("从响应数据中提取URL并发送请求的功能待实现");
        return responseData;
    }
}
