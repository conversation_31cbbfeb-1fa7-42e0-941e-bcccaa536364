package com.io661.extension.test;

import com.io661.extension.commonURL.CommonHttpUrl;
import com.io661.extension.util.SSLCertificateManager;

import java.io.IOException;

/**
 * HTTP连接测试类
 * 用于测试HTTP和HTTPS连接是否都能正常工作
 */
public class HttpTestMain {
    
    public static void main(String[] args) {
        System.out.println("开始测试HTTP/HTTPS连接...");

        // 初始化全局SSL配置
        initializeGlobalSSL();

        // 创建HTTP客户端
        CommonHttpUrl httpClient = new CommonHttpUrl();
        
        // 测试HTTP连接
        testHttpConnection(httpClient);
        
        // 测试HTTPS连接
        testHttpsConnection(httpClient);
        
        System.out.println("HTTP/HTTPS连接测试完成。");
    }

    /**
     * 初始化全局SSL配置
     */
    private static void initializeGlobalSSL() {
        try {
            SSLCertificateManager.addTrustedDomains(
                "io661.com",
                "open.io661.com",
                "s3.io661.com",
                "api.io661.com"
            );
            SSLCertificateManager.enableGlobalSSL();
            System.out.println("全局SSL配置已启用");
        } catch (Exception e) {
            System.err.println("SSL配置失败: " + e.getMessage());
        }
    }
    
    private static void testHttpConnection(CommonHttpUrl httpClient) {
        System.out.println("\n=== 测试HTTP连接 ===");
        
        try {
            // 测试HTTP GET请求到io661.com
            System.out.println("测试HTTP GET请求到 http://io661.com/");
            String response = httpClient.doGet("", null, null);
            System.out.println("HTTP GET请求成功！响应长度: " + response.length());
            System.out.println("响应前100个字符: " + response.substring(0, Math.min(100, response.length())));
            
        } catch (IOException e) {
            System.err.println("HTTP请求失败: " + e.getMessage());
        }
        
        try {
            // 测试HTTP POST请求
            System.out.println("\n测试HTTP POST请求...");
            String jsonBody = "{\"test\":\"data\"}";
            String postResponse = httpClient.doPost("api/test", jsonBody, null);
            System.out.println("HTTP POST请求成功！响应长度: " + postResponse.length());
        } catch (IOException e) {
            System.out.println("HTTP POST请求失败（可能是正常的）: " + e.getMessage());
        }
    }
    
    private static void testHttpsConnection(CommonHttpUrl httpClient) {
        System.out.println("\n=== 测试HTTPS连接 ===");
        
        // 临时修改BASE_URL为HTTPS进行测试
        try {
            // 这里我们需要创建一个新的实例来测试HTTPS
            CommonHttpUrl httpsClient = new CommonHttpUrl();
            // 注意：这里需要手动设置HTTPS URL，因为BASE_URL可能是HTTP
            
            System.out.println("测试HTTPS连接（如果BASE_URL是HTTPS）...");
            String response = httpsClient.doGet("", null, null);
            System.out.println("HTTPS GET请求成功！响应长度: " + response.length());
            
        } catch (IOException e) {
            System.out.println("HTTPS请求失败或BASE_URL不是HTTPS: " + e.getMessage());
        }
    }
}
