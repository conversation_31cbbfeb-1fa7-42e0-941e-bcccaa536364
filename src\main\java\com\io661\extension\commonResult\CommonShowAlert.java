package com.io661.extension.commonResult;

import javafx.application.Platform;
import javafx.scene.control.Alert;

public class CommonShowAlert {
    /**
     * 异步显示提示对话框
     */
    public static void showAlert(Alert.AlertType alertType, String title, String message) {
        Platform.runLater(() -> {
            Alert alert = new Alert(alertType);
            alert.setTitle(title);
            alert.setHeaderText(null);
            alert.setContentText(message);
            alert.showAndWait();
        });
    }

    /**
     * 同步显示提示对话框
     */
    public static void showSyncAlert(Alert.AlertType alertType, String title, String message) {
        if (javafx.application.Platform.isFxApplicationThread()) {
            // 如果已经在JavaFX线程中，直接显示
            Alert alert = new Alert(alertType);
            alert.setTitle(title);
            alert.setHeaderText(null);
            alert.setContentText(message);
            alert.showAndWait();
        } else {
            // 如果不在JavaFX线程中，使用CountDownLatch等待
            final java.util.concurrent.CountDownLatch latch = new java.util.concurrent.CountDownLatch(1);
            javafx.application.Platform.runLater(() -> {
                Alert alert = new Alert(alertType);
                alert.setTitle(title);
                alert.setHeaderText(null);
                alert.setContentText(message);
                alert.showAndWait();
                latch.countDown();
            });
            try {
                latch.await(); // 等待弹窗关闭
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }
}
