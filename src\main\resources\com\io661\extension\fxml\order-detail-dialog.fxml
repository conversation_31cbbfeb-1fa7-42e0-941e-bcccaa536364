<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.*?>
<?import java.net.URL?>
<BorderPane xmlns:fx="http://javafx.com/fxml/1" xmlns="http://javafx.com/javafx/21"
            fx:controller="com.io661.extension.controller.OrderDetailDialogController" prefWidth="600" prefHeight="700"
            styleClass="order-detail-dialog">
   <stylesheets>
      <URL value="@../css/order-styles.css" />
   </stylesheets>
   <top>
      <!-- 标题栏 -->
      <HBox alignment="CENTER_LEFT" spacing="10" styleClass="dialog-header">
         <padding>
            <Insets bottom="15" left="20" right="20" top="15" />
         </padding>
         <Label styleClass="dialog-title" text="订单详情" />
      </HBox>
   </top>

   <center>
      <ScrollPane fitToWidth="true" styleClass="detail-scroll-pane">
         <content>
            <VBox spacing="20" styleClass="detail-container">
               <padding>
                  <Insets bottom="20" left="20" right="20" top="20" />
               </padding>

               <!-- 订单进度条 -->
               <VBox spacing="15" styleClass="info-section">
                  <Label styleClass="section-title" text="订单进度" />
                  <HBox fx:id="progressContainer" spacing="0" styleClass="progress-container">
                     <!-- 进度条将在代码中动态生成 -->
                  </HBox>
               </VBox>

               <!-- 订单基本信息 -->
               <VBox spacing="10" styleClass="info-section">
                  <Label styleClass="section-title" text="订单信息" />
                  <GridPane hgap="15" vgap="10" styleClass="info-grid">
                     <columnConstraints>
                        <ColumnConstraints minWidth="100" prefWidth="120" />
                        <ColumnConstraints hgrow="ALWAYS" />
                     </columnConstraints>

                     <Label text="订单号:" GridPane.columnIndex="0" GridPane.rowIndex="0" styleClass="info-label" />
                     <Label fx:id="orderNoLabel" GridPane.columnIndex="1" GridPane.rowIndex="0" styleClass="info-value" />

                     <Label text="订单类型:" GridPane.columnIndex="0" GridPane.rowIndex="1" styleClass="info-label" />
                     <Label fx:id="orderTypeLabel" GridPane.columnIndex="1" GridPane.rowIndex="1" styleClass="info-value" />

                     <Label text="订单状态:" GridPane.columnIndex="0" GridPane.rowIndex="2" styleClass="info-label" />
                     <Label fx:id="orderStatusLabel" GridPane.columnIndex="1" GridPane.rowIndex="2" styleClass="info-value status-label" />

                     <Label text="创建时间:" GridPane.columnIndex="0" GridPane.rowIndex="3" styleClass="info-label" />
                     <Label fx:id="createTimeLabel" GridPane.columnIndex="1" GridPane.rowIndex="3" styleClass="info-value" />

                     <Label text="更新时间:" GridPane.columnIndex="0" GridPane.rowIndex="4" styleClass="info-label" />
                     <Label fx:id="updateTimeLabel" GridPane.columnIndex="1" GridPane.rowIndex="4" styleClass="info-value" />
                  </GridPane>
               </VBox>

               <!-- 价格信息 -->
               <VBox spacing="10" styleClass="info-section">
                  <Label styleClass="section-title" text="价格信息" />
                  <GridPane hgap="15" vgap="10" styleClass="info-grid">
                     <columnConstraints>
                        <ColumnConstraints minWidth="100" prefWidth="120" />
                        <ColumnConstraints hgrow="ALWAYS" />
                     </columnConstraints>

                     <Label text="订单价格:" GridPane.columnIndex="0" GridPane.rowIndex="0" styleClass="info-label" />
                     <Label fx:id="priceLabel" GridPane.columnIndex="1" GridPane.rowIndex="0" styleClass="info-value price-label" />

                     <Label text="实际到手:" GridPane.columnIndex="0" GridPane.rowIndex="1" styleClass="info-label" />
                     <Label fx:id="terminalPriceLabel" GridPane.columnIndex="1" GridPane.rowIndex="1" styleClass="info-value price-label" />

                     <Label text="手续费:" GridPane.columnIndex="0" GridPane.rowIndex="2" styleClass="info-label" />
                     <Label fx:id="feeLabel" GridPane.columnIndex="1" GridPane.rowIndex="2" styleClass="info-value" />
                  </GridPane>
               </VBox>

               <!-- 物品信息 -->
               <VBox spacing="10" styleClass="info-section">
                  <Label styleClass="section-title" text="物品信息" />
                  <HBox spacing="15" alignment="CENTER_LEFT">
                     <!-- 物品图标 -->
                     <ImageView fx:id="itemImageView" fitHeight="80" fitWidth="80" preserveRatio="true" styleClass="item-image" />

                     <!-- 物品详情 -->
                     <VBox spacing="8" HBox.hgrow="ALWAYS">
                        <Label fx:id="itemNameLabel" styleClass="item-name" />
                        <Label fx:id="hashNameLabel" styleClass="item-hash-name" />
                        <HBox spacing="10" alignment="CENTER_LEFT">
                           <Label text="磨损值:" styleClass="info-label" />
                           <Label fx:id="floatValueLabel" styleClass="info-value" />
                        </HBox>
                        <HBox spacing="10" alignment="CENTER_LEFT">
                           <Label fx:id="qualityLabel" styleClass="quality-tag" />
                           <Label fx:id="rarityLabel" styleClass="rarity-tag" />
                           <Label fx:id="exteriorLabel" styleClass="exterior-tag" />
                        </HBox>
                     </VBox>
                  </HBox>
               </VBox>

               <!-- 买家信息 -->
               <VBox spacing="10" styleClass="info-section">
                  <Label styleClass="section-title" text="买家信息" />
                  <HBox spacing="15" alignment="CENTER_LEFT">
                     <ImageView fx:id="buyerAvatarImageView" fitHeight="50" fitWidth="50" preserveRatio="true" styleClass="user-avatar" />
                     <VBox spacing="5">
                        <Label fx:id="buyerNicknameLabel" styleClass="user-nickname" />
                        <HBox spacing="10" alignment="CENTER_LEFT">
                           <Label text="注册时间:" styleClass="info-label" />
                           <Label fx:id="buyerRegDateLabel" styleClass="info-value" />
                        </HBox>
                     </VBox>
                  </HBox>
               </VBox>

               <!-- 卖家信息 -->
               <VBox spacing="10" styleClass="info-section">
                  <Label styleClass="section-title" text="卖家信息" />
                  <HBox spacing="15" alignment="CENTER_LEFT">
                     <ImageView fx:id="sellerAvatarImageView" fitHeight="50" fitWidth="50" preserveRatio="true" styleClass="user-avatar" />
                     <VBox spacing="5">
                        <Label fx:id="sellerNicknameLabel" styleClass="user-nickname" />
                        <HBox spacing="10" alignment="CENTER_LEFT">
                           <Label text="注册时间:" styleClass="info-label" />
                           <Label fx:id="sellerRegDateLabel" styleClass="info-value" />
                        </HBox>
                     </VBox>
                  </HBox>
               </VBox>
            </VBox>
         </content>
      </ScrollPane>
   </center>

   <bottom>
      <!-- 底部按钮 -->
      <HBox alignment="CENTER_RIGHT" spacing="10" styleClass="dialog-footer">
         <padding>
            <Insets bottom="15" left="20" right="20" top="15" />
         </padding>
         <Button fx:id="refreshDetailButton" styleClass="secondary-button" text="刷新详情" />
         <Button fx:id="confirmButton" styleClass="primary-button" text="确定" />
      </HBox>
   </bottom>
</BorderPane>
