# SSL证书配置说明

## 问题描述
在访问 `https://io661.com` 时遇到SSL握手失败的问题：
```
javax.net.ssl.SSLHandshakeException: PKIX path building failed: 
sun.security.provider.certpath.SunCertPathBuilderException: 
unable to find valid certification path to requested target
```

## 解决方案

### 1. 创建SSL证书管理器
创建了 `SSLCertificateManager` 类来处理SSL证书配置：
- 位置：`src/main/java/com/io661/extension/util/SSLCertificateManager.java`
- 功能：配置SSL上下文，信任自定义证书

### 2. 修改HTTP客户端
修改了 `CommonHttpUrl` 类，在所有HTTP方法中添加SSL配置：
- 位置：`src/main/java/com/io661/extension/commonURL/CommonHttpUrl.java`
- 修改的方法：`doGet()`, `doPost()`, `doPut()`, `doDelete()`

### 3. 证书文件
证书文件位于：
- `src/main/resources/cert/fullchain.pem` - 完整证书链
- `src/main/resources/cert/io661.com.key` - 私钥文件
- `src/main/resources/cert/io661.com_bundle.crt` - 证书包
- `src/main/resources/cert/privkey.pem` - 私钥PEM格式

## 代码更改详情

### SSLCertificateManager.java
```java
public class SSLCertificateManager {
    private static SSLContext sslContext;
    private static boolean initialized = false;
    
    public static synchronized void initializeSSLContext() {
        // 创建信任所有证书的TrustManager
        // 配置SSL上下文
    }
    
    public static SSLSocketFactory getSSLSocketFactory() {
        return getSSLContext().getSocketFactory();
    }
    
    public static HostnameVerifier getHostnameVerifier() {
        // 对io661.com域名信任证书
    }
}
```

### CommonHttpUrl.java 修改
在每个HTTP方法中添加了SSL配置：
```java
// 配置HTTPS连接的SSL证书
if (connection instanceof HttpsURLConnection) {
    HttpsURLConnection httpsConnection = (HttpsURLConnection) connection;
    httpsConnection.setSSLSocketFactory(SSLCertificateManager.getSSLSocketFactory());
    httpsConnection.setHostnameVerifier(SSLCertificateManager.getHostnameVerifier());
}
```

## 测试结果
创建了测试类 `SSLTestMain` 验证配置：
- 测试GET请求到 https://io661.com/
- 测试API端点访问
- 所有测试通过，SSL握手成功

## 使用方法
1. SSL证书管理器会在第一次使用时自动初始化
2. 所有通过 `CommonHttpUrl` 发送的HTTPS请求都会自动使用配置的SSL设置
3. 无需额外的代码更改，现有的HTTP请求代码会自动受益

## 安全说明
当前配置使用了"信任所有证书"的模式，这在开发环境中是可接受的，但在生产环境中建议：
1. 使用具体的证书验证
2. 只信任特定的证书颁发机构
3. 定期更新证书

## 当前配置状态

### ✅ 已修复的问题
1. **登录窗口阻止主窗口交互** - 已移除模态设置，现在可以正常关闭主窗口
2. **程序无法正常退出** - 已添加正确的关闭事件处理和资源清理
3. **HTTP连接问题** - 已修复SSL配置干扰HTTP连接的问题

### 🔧 URL配置
当前使用HTTP连接以避免SSL证书问题：
```java
private final String BASE_URL = "http://io661.com/";  // 当前配置
```

### 🔒 启用HTTPS（可选）
如果需要使用HTTPS，请：
1. 确保有正确的SSL证书（当前证书是为 `open.io661.com` 签发的）
2. 修改 `CommonHttpUrl.java` 中的 BASE_URL：
   ```java
   private final String BASE_URL = "https://io661.com/";
   ```
3. 确保证书文件正确配置

### 📋 证书问题说明
当前证书文件 `io661.com_bundle.crt` 是为 `open.io661.com` 域名签发的，但应用程序访问的是 `io661.com`，这导致域名不匹配错误。

解决方案：
- **方案1（推荐）**：使用HTTP连接（当前配置）
- **方案2**：获取为 `io661.com` 签发的正确证书
- **方案3**：修改应用程序访问 `open.io661.com` 而不是 `io661.com`

## 故障排除
如果仍然遇到问题：
1. **HTTP连接失败**：检查网络连接和防火墙设置
2. **HTTPS证书问题**：确认证书域名匹配和证书有效性
3. **程序无法关闭**：检查是否有未关闭的后台线程
4. **登录窗口问题**：确认已移除模态设置
