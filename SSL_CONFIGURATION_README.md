# SSL证书配置说明

## 问题描述
在访问 `https://io661.com` 时遇到SSL握手失败的问题：
```
javax.net.ssl.SSLHandshakeException: PKIX path building failed: 
sun.security.provider.certpath.SunCertPathBuilderException: 
unable to find valid certification path to requested target
```

## 解决方案

### 1. 创建SSL证书管理器
创建了 `SSLCertificateManager` 类来处理SSL证书配置：
- 位置：`src/main/java/com/io661/extension/util/SSLCertificateManager.java`
- 功能：配置SSL上下文，信任自定义证书

### 2. 修改HTTP客户端
修改了 `CommonHttpUrl` 类，在所有HTTP方法中添加SSL配置：
- 位置：`src/main/java/com/io661/extension/commonURL/CommonHttpUrl.java`
- 修改的方法：`doGet()`, `doPost()`, `doPut()`, `doDelete()`

### 3. 证书文件
证书文件位于：
- `src/main/resources/cert/fullchain.pem` - 完整证书链
- `src/main/resources/cert/io661.com.key` - 私钥文件
- `src/main/resources/cert/io661.com_bundle.crt` - 证书包
- `src/main/resources/cert/privkey.pem` - 私钥PEM格式

## 代码更改详情

### SSLCertificateManager.java
```java
public class SSLCertificateManager {
    private static SSLContext sslContext;
    private static boolean initialized = false;
    
    public static synchronized void initializeSSLContext() {
        // 创建信任所有证书的TrustManager
        // 配置SSL上下文
    }
    
    public static SSLSocketFactory getSSLSocketFactory() {
        return getSSLContext().getSocketFactory();
    }
    
    public static HostnameVerifier getHostnameVerifier() {
        // 对io661.com域名信任证书
    }
}
```

### CommonHttpUrl.java 修改
在每个HTTP方法中添加了SSL配置：
```java
// 配置HTTPS连接的SSL证书
if (connection instanceof HttpsURLConnection) {
    HttpsURLConnection httpsConnection = (HttpsURLConnection) connection;
    httpsConnection.setSSLSocketFactory(SSLCertificateManager.getSSLSocketFactory());
    httpsConnection.setHostnameVerifier(SSLCertificateManager.getHostnameVerifier());
}
```

## 测试结果
创建了测试类 `SSLTestMain` 验证配置：
- 测试GET请求到 https://io661.com/
- 测试API端点访问
- 所有测试通过，SSL握手成功

## 使用方法
1. SSL证书管理器会在第一次使用时自动初始化
2. 所有通过 `CommonHttpUrl` 发送的HTTPS请求都会自动使用配置的SSL设置
3. 无需额外的代码更改，现有的HTTP请求代码会自动受益

## 安全说明
当前配置使用了"信任所有证书"的模式，这在开发环境中是可接受的，但在生产环境中建议：
1. 使用具体的证书验证
2. 只信任特定的证书颁发机构
3. 定期更新证书

## 故障排除
如果仍然遇到SSL问题：
1. 检查证书文件是否存在于正确位置
2. 确认证书文件格式正确
3. 查看控制台输出的SSL初始化日志
4. 验证网络连接和防火墙设置
