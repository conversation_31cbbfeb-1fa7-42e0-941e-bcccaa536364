package com.io661.extension.test;

import com.io661.extension.util.SSLCertificateManager;
import com.io661.extension.util.HttpClientUtil;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 全局SSL配置测试类
 * 测试多域名HTTPS访问和动态URL处理
 */
public class GlobalSSLTestMain {
    
    public static void main(String[] args) {
        System.out.println("开始测试全局SSL配置...");
        
        // 初始化全局SSL配置
        initializeGlobalSSL();
        
        // 测试多域名HTTPS访问
        testMultipleDomains();
        
        // 测试动态URL处理
        testDynamicUrls();
        
        System.out.println("全局SSL配置测试完成。");
    }
    
    /**
     * 初始化全局SSL配置
     */
    private static void initializeGlobalSSL() {
        System.out.println("\n=== 初始化全局SSL配置 ===");
        
        try {
            // 添加信任的域名
            SSLCertificateManager.addTrustedDomains(
                "io661.com",
                "open.io661.com", 
                "s3.io661.com",
                "api.io661.com",
                "cdn.io661.com",
                "static.io661.com",
                "img.io661.com"
            );
            
            // 启用全局SSL配置
            SSLCertificateManager.enableGlobalSSL();
            
            System.out.println("全局SSL配置已启用");
            System.out.println("信任的域名: " + SSLCertificateManager.getTrustedDomains());
            
        } catch (Exception e) {
            System.err.println("初始化全局SSL配置失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试多域名HTTPS访问
     */
    private static void testMultipleDomains() {
        System.out.println("\n=== 测试多域名HTTPS访问 ===");
        
        String[] testUrls = {
            "https://io661.com/",
            "https://open.io661.com/",
            "https://s3.io661.com/",
            "https://api.io661.com/",
            "http://io661.com/"  // 测试HTTP也能正常工作
        };
        
        Map<String, String> headers = new HashMap<>();
        headers.put("User-Agent", "GlobalSSLTest/1.0");
        
        for (String url : testUrls) {
            try {
                System.out.println("\n测试URL: " + url);
                String response = HttpClientUtil.doGet(url, headers);
                System.out.println("✅ 请求成功！响应长度: " + response.length());
                System.out.println("响应前50个字符: " + response.substring(0, Math.min(50, response.length())));
                
            } catch (IOException e) {
                System.out.println("❌ 请求失败: " + e.getMessage());
            }
        }
    }
    
    /**
     * 测试动态URL处理
     */
    private static void testDynamicUrls() {
        System.out.println("\n=== 测试动态URL处理 ===");
        
        // 模拟从API响应中获取的动态URL
        String[] dynamicUrls = {
            "https://s3.io661.com/images/avatar.jpg",
            "https://cdn.io661.com/assets/style.css",
            "https://api.io661.com/v1/user/profile",
            "https://static.io661.com/js/app.js"
        };
        
        Map<String, String> headers = new HashMap<>();
        headers.put("Accept", "*/*");
        
        for (String url : dynamicUrls) {
            try {
                System.out.println("\n测试动态URL: " + url);
                String response = HttpClientUtil.doGet(url, headers);
                System.out.println("✅ 动态URL请求成功！响应长度: " + response.length());
                
            } catch (IOException e) {
                System.out.println("❌ 动态URL请求失败: " + e.getMessage());
            }
        }
    }
    
    /**
     * 测试POST请求到HTTPS端点
     */
    private static void testHttpsPost() {
        System.out.println("\n=== 测试HTTPS POST请求 ===");
        
        try {
            String url = "https://api.io661.com/v1/test";
            String jsonBody = "{\"test\":\"data\",\"timestamp\":" + System.currentTimeMillis() + "}";
            
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("Accept", "application/json");
            
            String response = HttpClientUtil.doPost(url, jsonBody, headers);
            System.out.println("✅ HTTPS POST请求成功！响应: " + response);
            
        } catch (IOException e) {
            System.out.println("❌ HTTPS POST请求失败: " + e.getMessage());
        }
    }
}
