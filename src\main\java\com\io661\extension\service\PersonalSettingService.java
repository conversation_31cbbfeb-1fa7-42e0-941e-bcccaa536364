package com.io661.extension.service;

import com.io661.extension.model.Enum.PlatEnum;
import com.io661.extension.model.User.PersonalSettings;

import java.util.List;
import java.util.Map;

public interface PersonalSettingService {
    /**
     * 设置平台费率
     * @param platEnums 平台代码列表
     * @param ratio 比例
     * @return 设置结果
     */
    List<PlatEnum> setPlatRation(List<PlatEnum> platEnums, int ratio);

    /**
     * 获取个人设置配置
     * @return 个人设置对象
     */
    PersonalSettings getPersonalSettings();

    /**
     * 更新个人设置配置
     * @param settings 个人设置对象
     * @return 是否更新成功
     */
    boolean updatePersonalSettings(PersonalSettings settings);

    /**
     * 重置个人设置为默认值
     * @return 是否重置成功
     */
    boolean resetPersonalSettings();

    /**
     * 获取平台费率配置（兼容旧接口）
     * @return 平台费率映射 (平台名称 -> 费率百分比)
     */
    Map<String, Double> getPlatformFeeRates();

    /**
     * 设置单个平台费率（兼容旧接口）
     * @param platform 平台名称 (io661, youpin等)
     * @param feeRate 费率 (0.0-1.0, 例如0.05表示5%)
     * @return 是否设置成功
     */
    boolean setPlatformFeeRate(String platform, double feeRate);

    /**
     * 根据到手价计算上架价格
     * @param platform 平台名称
     * @param targetPrice 期望到手价(分)
     * @return 需要设置的上架价格(分)
     */
    int calculateListingPrice(String platform, int targetPrice);

    /**
     * 根据上架价格计算到手价
     * @param platform 平台名称
     * @param listingPrice 上架价格(分)
     * @return 实际到手价(分)
     */
    int calculateReceivedPrice(String platform, int listingPrice);
}
