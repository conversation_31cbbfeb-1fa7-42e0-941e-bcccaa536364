package com.io661.extension.util.SQLite;

import com.io661.extension.model.User.PersonalSettings;

import java.nio.file.Paths;
import java.sql.*;

public class SqliteManager {
    public static String DATABASE_PATH = Paths.get(System.getProperty("user.dir"), "src", "main", "resources", "SQLite", "io661extension.db").toString();
    public static Connection connection;

    static {
        connectDatabase();
    }

    public static void connectDatabase() {
        // 打印尝试连接的路径，方便调试
        System.out.println("链接数据库失败: " + DATABASE_PATH);
        try {
            if (connection == null || connection.isClosed()) {
                connection = DriverManager.getConnection("jdbc:sqlite:" + DATABASE_PATH);
                System.out.println("连接成功: " + DATABASE_PATH);
                // 创建表（如果不存在） - 可选，但对于首次运行很方便
                createTableIfNotExists();
            }
        } catch (SQLException e) {
            System.err.println("连接失败: " + DATABASE_PATH);
            throw new RuntimeException("数据库连接失败路径: " + DATABASE_PATH, e);
        }
    }

    /**
     * 创建 YouPin_user_steam_bind 表和 personal_settings 表（如果它们不存在）
     * 同时确保 steam_id 是唯一的，这样我们可以进行 UPSERT 操作或避免重复
     */
    private static void createTableIfNotExists() {
        // YouPin_user_steam_bind 表
        String sqlCreateYouPinTable = "CREATE TABLE IF NOT EXISTS YouPin_user_steam_bind (" +
                "id INTEGER PRIMARY KEY AUTOINCREMENT, " +
                "steam_id TEXT UNIQUE NOT NULL, " +
                "authorization TEXT NOT NULL, " +
                "create_time TEXT NOT NULL DEFAULT (strftime('%Y-%m-%d %H:%M:%S', 'now', 'localtime')), " +
                "update_time TEXT NOT NULL DEFAULT (strftime('%Y-%m-%d %H:%M:%S', 'now', 'localtime'))" +
                ");";

        // 删除旧的平台费率配置表
        String sqlDropOldTable = "DROP TABLE IF EXISTS platform_fee_rates;";

        // 新的个人设置表
        String sqlCreatePersonalSettingsTable = "CREATE TABLE IF NOT EXISTS personal_settings (" +
                "id INTEGER PRIMARY KEY AUTOINCREMENT, " +
                "IO661_trans_ratio REAL NOT NULL DEFAULT 0.05, " +
                "IO661_withdraw_ratio REAL NOT NULL DEFAULT 0.05, " +
                "YouPin_trans_ratio REAL NOT NULL DEFAULT 0.10, " +
                "YouPin_withdraw_ratio REAL NOT NULL DEFAULT 0.10, " +
                "create_time TEXT NOT NULL DEFAULT (strftime('%Y-%m-%d %H:%M:%S', 'now', 'localtime')), " +
                "update_time TEXT NOT NULL DEFAULT (strftime('%Y-%m-%d %H:%M:%S', 'now', 'localtime'))" +
                ");";

        // 触发器用于自动更新 YouPin_user_steam_bind 的 update_time
        String sqlCreateYouPinTrigger = "CREATE TRIGGER IF NOT EXISTS trg_youpin_cookie_update_time " +
                "AFTER UPDATE ON YouPin_user_steam_bind " +
                "FOR EACH ROW " +
                "WHEN OLD.authorization IS NOT NEW.authorization " + // 仅当 authorization 变化时更新
                "BEGIN " +
                "    UPDATE YouPin_user_steam_bind SET update_time = (strftime('%Y-%m-%d %H:%M:%S', 'now', 'localtime')) WHERE id = OLD.id; " +
                "END;";

        // 触发器用于自动更新 personal_settings 的 update_time
        String sqlCreatePersonalSettingsTrigger = "CREATE TRIGGER IF NOT EXISTS trg_personal_settings_update_time " +
                "AFTER UPDATE ON personal_settings " +
                "FOR EACH ROW " +
                "BEGIN " +
                "    UPDATE personal_settings SET update_time = (strftime('%Y-%m-%d %H:%M:%S', 'now', 'localtime')) WHERE id = OLD.id; " +
                "END;";

        try (Statement stmt = connection.createStatement()) {
            stmt.execute(sqlCreateYouPinTable);
            stmt.execute(sqlDropOldTable); // 删除旧表
            stmt.execute(sqlCreatePersonalSettingsTable);
            stmt.execute(sqlCreateYouPinTrigger);
            stmt.execute(sqlCreatePersonalSettingsTrigger);

            // 插入默认设置记录（如果不存在）
            String sqlInsertDefault = "INSERT OR IGNORE INTO personal_settings (id) VALUES (1);";
            stmt.execute(sqlInsertDefault);

            System.out.println("数据库表创建成功，已删除旧的platform_fee_rates表并创建新的personal_settings表");
        } catch (SQLException e) {
            System.err.println("创建数据库表失败: " + e.getMessage());
            // 如果表结构重要，可以考虑抛出异常
        }
    }


    /**
     * 增加或更新YouPin cookie。
     * 如果指定 steamId 的记录已存在，则更新 authorization 和 update_time。
     * 否则，插入新记录。
     * @param steamId     steamId
     * @param cookieValue authorization
     * @return 是否成功
     */
    public static boolean upsertYouPinCookie(String steamId, String cookieValue) {
        // SQLITE UPSERT (INSERT OR REPLACE or ON CONFLICT DO UPDATE)
        // 使用 ON CONFLICT 需要 steam_id 列有 UNIQUE 约束
        String sql = "INSERT INTO YouPin_user_steam_bind (steam_id, authorization) VALUES (?, ?) " +
                "ON CONFLICT(steam_id) DO UPDATE SET authorization = excluded.authorization, update_time = (strftime('%Y-%m-%d %H:%M:%S', 'now', 'localtime'));";

        if (steamId == null || steamId.trim().isEmpty()) {
            System.err.println("steamId不能为空");
            return false;
        }
        if (cookieValue == null) { // cookieValue 可以为空字符串，表示清除
            System.err.println("CookieValue不能为空");
            return false;
        }


        try (PreparedStatement preparedStatement = connection.prepareStatement(sql)) {
            preparedStatement.setString(1, steamId);
            preparedStatement.setString(2, cookieValue);
            preparedStatement.executeUpdate();
            return true;
        } catch (SQLException e) {
            System.err.println("增加或更新Cookie失败" + steamId + ": " + e.getMessage());
            // 决定是否抛出，对于manager类，返回false可能更友好
            // throw new RuntimeException("Upserting Cookie失败: " + e.getMessage(), e);
            return false;
        }
    }

    /**
     * 查询YouPin数据库cookie
     * @param steamId steamId
     * @return cookie, 如果未找到则返回 null
     */
    public static String queryYouPinCookie(String steamId) {
        if (steamId == null || steamId.trim().isEmpty()) {
            System.err.println("steamId不能为空");
            return null;
        }
        String sql = "SELECT authorization FROM YouPin_user_steam_bind WHERE steam_id = ?";
        try (PreparedStatement preparedStatement = connection.prepareStatement(sql)) {
            preparedStatement.setString(1, steamId);
            try (ResultSet resultSet = preparedStatement.executeQuery()) {
                if (resultSet.next()) {
                    return resultSet.getString("authorization");
                }
            }
        } catch (SQLException e) {
            System.err.println("查询Cookie失败" + steamId + ": " + e.getMessage());
            // throw new RuntimeException("查询Cookie失败: " + e.getMessage(), e);
        }
        return null;
    }

    /**
     * 删除指定 steamId 的 YouPin cookie
     * @param steamId steamId
     * @return 是否成功删除 (如果记录存在并被删除则为 true)
     */
    public static boolean deleteYouPinCookie(String steamId) {
        if (steamId == null || steamId.trim().isEmpty()) {
            System.err.println("steamId不能为空");
            return false;
        }
        String sql = "DELETE FROM YouPin_user_steam_bind WHERE steam_id = ?";
        try (PreparedStatement preparedStatement = connection.prepareStatement(sql)) {
            preparedStatement.setString(1, steamId);
            int rowsAffected = preparedStatement.executeUpdate();
            return rowsAffected > 0;
        } catch (SQLException e) {
            System.err.println("删除Cookie失败" + steamId + ": " + e.getMessage());
            // throw new RuntimeException("删除Cookie失败: " + e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查指定 steamId 的 YouPin cookie 是否存在
     * @param steamId steamId
     * @return 如果存在则为 true, 否则为 false
     */
    public static boolean cookieExists(String steamId) {
        return queryYouPinCookie(steamId) != null;
    }

    // ==================== 个人设置管理方法 ====================

    /**
     * 获取个人设置配置
     * @return 个人设置对象，如果不存在则返回默认值
     */
    public static PersonalSettings getPersonalSettings() {
        String sql = "SELECT * FROM personal_settings WHERE id = 1";
        try (PreparedStatement preparedStatement = connection.prepareStatement(sql)) {
            try (ResultSet resultSet = preparedStatement.executeQuery()) {
                if (resultSet.next()) {
                    PersonalSettings settings = new PersonalSettings();
                    settings.setId(resultSet.getInt("id"));
                    settings.setIo661TransRatio(resultSet.getDouble("IO661_trans_ratio"));
                    settings.setIo661WithdrawRatio(resultSet.getDouble("IO661_withdraw_ratio"));
                    settings.setYouPinTransRatio(resultSet.getDouble("YouPin_trans_ratio"));
                    settings.setYouPinWithdrawRatio(resultSet.getDouble("YouPin_withdraw_ratio"));
                    settings.setCreateTime(resultSet.getString("create_time"));
                    settings.setUpdateTime(resultSet.getString("update_time"));
                    return settings;
                }
            }
        } catch (SQLException e) {
            System.err.println("查询个人设置失败: " + e.getMessage());
        }

        // 如果没有找到记录，返回默认设置
        PersonalSettings defaultSettings = new PersonalSettings();
        defaultSettings.setId(1);
        defaultSettings.setIo661TransRatio(0.00);
        defaultSettings.setIo661WithdrawRatio(0.01);
        defaultSettings.setYouPinTransRatio(0.01);
        defaultSettings.setYouPinWithdrawRatio(0.01);
        return defaultSettings;
    }

    /**
     * 更新个人设置配置
     * @param settings 个人设置对象
     * @return 是否成功
     */
    public static boolean updatePersonalSettings(PersonalSettings settings) {
        if (settings == null) {
            System.err.println("个人设置对象不能为空");
            return false;
        }

        // 验证费率范围
        if (settings.getIo661TransRatio() < 0 || settings.getIo661TransRatio() > 1 ||
            settings.getIo661WithdrawRatio() < 0 || settings.getIo661WithdrawRatio() > 1 ||
            settings.getYouPinTransRatio() < 0 || settings.getYouPinTransRatio() > 1 ||
            settings.getYouPinWithdrawRatio() < 0 || settings.getYouPinWithdrawRatio() > 1) {
            System.err.println("费率必须在0-1之间");
            return false;
        }

        String sql = "UPDATE personal_settings SET " +
                "IO661_trans_ratio = ?, " +
                "IO661_withdraw_ratio = ?, " +
                "YouPin_trans_ratio = ?, " +
                "YouPin_withdraw_ratio = ? " +
                "WHERE id = 1";

        try (PreparedStatement preparedStatement = connection.prepareStatement(sql)) {
            preparedStatement.setDouble(1, settings.getIo661TransRatio());
            preparedStatement.setDouble(2, settings.getIo661WithdrawRatio());
            preparedStatement.setDouble(3, settings.getYouPinTransRatio());
            preparedStatement.setDouble(4, settings.getYouPinWithdrawRatio());

            int rowsAffected = preparedStatement.executeUpdate();
            if (rowsAffected > 0) {
                System.out.println("个人设置更新成功");
                return true;
            } else {
                System.err.println("个人设置更新失败：没有找到记录");
                return false;
            }
        } catch (SQLException e) {
            System.err.println("更新个人设置失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 重置个人设置为默认值
     * @return 是否成功
     */
    public static boolean resetPersonalSettings() {
        String sql = "UPDATE personal_settings SET " +
                "IO661_trans_ratio = 0.00, " +
                "IO661_withdraw_ratio = 0.01, " +
                "YouPin_trans_ratio = 0.01, " +
                "YouPin_withdraw_ratio = 0.01 " +
                "WHERE id = 1";

        try (PreparedStatement preparedStatement = connection.prepareStatement(sql)) {
            int rowsAffected = preparedStatement.executeUpdate();
            if (rowsAffected > 0) {
                System.out.println("个人设置已重置为默认值");
                return true;
            } else {
                System.err.println("重置个人设置失败：没有找到记录");
                return false;
            }
        } catch (SQLException e) {
            System.err.println("重置个人设置失败: " + e.getMessage());
            return false;
        }
    }


}