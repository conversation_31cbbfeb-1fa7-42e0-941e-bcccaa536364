<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.image.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.shape.Circle?>

<VBox prefHeight="231.0" prefWidth="280" styleClass="account-card" stylesheets="@../css/account-card.css" xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1">
    <padding>
        <Insets bottom="12" left="12" right="12" top="12" />
    </padding>

    <!-- 顶部信息区域 -->
    <HBox alignment="CENTER_LEFT" spacing="10">
        <!-- 头像 -->
        <StackPane styleClass="avatar-container">
            <Circle fx:id="avatarCircle" radius="20" styleClass="avatar-circle" />
            <ImageView fx:id="avatarImage" fitHeight="40" fitWidth="40" pickOnBounds="true" preserveRatio="true">
                <Image url="@../img/avatar.png" />
            </ImageView>
        </StackPane>

        <!-- 用户名和状态 -->
        <VBox spacing="2">
            <HBox alignment="CENTER_LEFT" spacing="5">
                <!-- 机器人/人工图标 -->
                <ImageView fx:id="accountTypeIcon" fitHeight="16.0" fitWidth="16.0" preserveRatio="true">
                    <Image url="@../img/human.png" />
                </ImageView>
                <Label fx:id="usernameLabel" styleClass="username-label" text="Username" />
            </HBox>
            <Label fx:id="balanceLabel" styleClass="account-status-label" text="账号状态" />
        </VBox>

        <Region HBox.hgrow="ALWAYS" />
        <Button fx:id="refreshButton" mnemonicParsing="false" styleClass="card-icon-button">
            <graphic>
                <ImageView fitHeight="16.0" fitWidth="16.0">
                    <Image url="@../img/refresh.png" />
                </ImageView>
            </graphic>
        </Button>

        <Button fx:id="settingsButton" mnemonicParsing="false" styleClass="card-icon-button">
            <graphic>
                <ImageView fitHeight="16.0" fitWidth="16.0">
                    <Image url="@../img/settings.png" />
                </ImageView>
            </graphic>
        </Button>
    </HBox>

    <Separator styleClass="card-separator">
        <VBox.margin>
            <Insets bottom="8" top="8" />
        </VBox.margin>
    </Separator>

    <!-- 账号详细信息 -->
    <GridPane hgap="10" vgap="6">
        <columnConstraints>
            <ColumnConstraints hgrow="SOMETIMES" minWidth="80" prefWidth="80" />
            <ColumnConstraints hgrow="SOMETIMES" minWidth="10" prefWidth="100" />
        </columnConstraints>

        <Label styleClass="info-label" text="ID:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
        <Label fx:id="accountIdLabel" styleClass="info-value" text="----------" GridPane.columnIndex="1" GridPane.rowIndex="0" />

        <Label styleClass="info-label" text="交易URL:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
        <HBox alignment="CENTER_LEFT" spacing="5" GridPane.columnIndex="1" GridPane.rowIndex="1">
            <Label fx:id="tradeUrlLabel" styleClass="info-value" text="...partner=123456&amp;token=ABC" />
            <Button fx:id="copyTradeUrlButton" mnemonicParsing="false" styleClass="copy-button">
                <graphic>
                    <ImageView fitHeight="12.0" fitWidth="12.0">
                        <Image url="@../img/copy.png" />
                    </ImageView>
                </graphic>
            </Button>
        </HBox>

        <Label styleClass="info-label" text="IP配置:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
        <Label fx:id="ipConfigLabel" styleClass="info-value" text="内置代理" GridPane.columnIndex="1" GridPane.rowIndex="2" />

        <Label styleClass="info-label" text="代理状态:" GridPane.columnIndex="0" GridPane.rowIndex="3" />
        <HBox alignment="CENTER_LEFT" spacing="5" GridPane.columnIndex="1" GridPane.rowIndex="3">
            <Circle fx:id="proxyStatusCircle" radius="4" styleClass="proxy-status-circle" />
            <Label fx:id="proxyStatusLabel" styleClass="proxy-status-label" text="正常" />
            <Region HBox.hgrow="ALWAYS" />
            <Button fx:id="proxyInfoButton" mnemonicParsing="false" styleClass="info-button">
                <graphic>
                    <ImageView fitHeight="12.0" fitWidth="12.0">
                        <Image url="@../img/info.png" />
                    </ImageView>
                </graphic>
            </Button>
        </HBox>

        <Label styleClass="info-label" text="已绑定平台:" GridPane.columnIndex="0" GridPane.rowIndex="4" />
        <HBox fx:id="boundPlatformsBox" alignment="CENTER_LEFT" spacing="5" GridPane.columnIndex="1" GridPane.rowIndex="4">
        </HBox>
      <rowConstraints>
         <RowConstraints />
         <RowConstraints />
         <RowConstraints />
         <RowConstraints />
         <RowConstraints />
      </rowConstraints>
    </GridPane>

    <Region VBox.vgrow="ALWAYS" />

    <!-- 底部按钮 -->
    <HBox alignment="CENTER" spacing="10">
        <Button fx:id="viewTokenButton" maxWidth="Infinity" prefHeight="31.0" prefWidth="263.0" styleClass="card-action-button" text="查看令牌" HBox.hgrow="ALWAYS" />
    </HBox>
</VBox>
