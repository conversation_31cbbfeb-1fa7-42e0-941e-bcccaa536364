package com.io661.extension.service;

import com.io661.extension.model.User.*;

public interface WithdrawService {
    /**
     * 提现
     * @param token   用户的令牌
     * @return 用户信息的JSON字符串，如果获取失败返回null
     */
    Object withdraw(WithdrawReq req, String token);

    /**
     * 获取提现历史
     * @param token   用户的令牌
     * @return 用户信息的JSON字符串，如果获取失败返回null
     * @param req 提现历史请求参数
     */
    WithdrawHistoryRes getWithdrawHistory(WithdrawHistoryReq req, String token);

    /**
     * 获取流水历史
     * @param token   用户的令牌
     * @return 用户信息的JSON字符串，如果获取失败返回null
     * @param req 流水历史请求参数
     */
    DetailHistoryRes detailHistory(DetailHistoryReq req, String token);
}
