package com.io661.extension.service;

import com.io661.extension.model.User.EditUserInfoReq;
import com.io661.extension.model.User.UserInfoRes;

public interface UserService {
    /**
     * 验证令牌有效性
     *
     * @param cookie 授权令牌
     * @return 如果令牌有效返回true，否则返回false
     */
    boolean validateToken(String cookie);

    /**
     * 获取用户信息
     *
     * @param cookie 授权令牌
     * @return 用户信息的JSON字符串，如果获取失败返回null
     */
    UserInfoRes.Data_ getUserInfo(String cookie);

    /**
     * 修改用户信息
     * @param request 修改用户信息的请求对象
     * @param cookie   用户的令牌
     * @return 用户信息的JSON字符串，如果获取失败返回null
     */
    boolean editUserInfo(EditUserInfoReq request, String cookie);

    /**
     * 退出登录
     *
     * @param cookie 授权令牌
     * @return 用户信息的JSON字符串，如果获取失败返回null
     */
    boolean logout(String cookie);

}
