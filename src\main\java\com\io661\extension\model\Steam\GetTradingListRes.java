package com.io661.extension.model.Steam;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class GetTradingListRes {
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 展示图片
     */
    private String iconUrl;
    /**
     * 模板hashName
     */
    private String hashName;
    /**
     * 模板展示昵称
     */
    private String itemName;
    /**
     * 磨损值
     */
    private BigDecimal floatValue;
    /**
     * 买家steam信息
     */
    private Steam buyerInfo;
    /**
     * 卖家steam信息
     */
    private Steam sellerInfo;
    /**
     * 商品金额（分）
     */
    private Integer price;
    /**
     * 下单时间
     */
    private LocalDateTime createTime;

    @Data
    public static class Steam {
        /**
         * 头像地址
         */
        private String avatar;
        /**
         * 用户昵称
         */
        private String nickname;
        /**
         * 注册时间
         */
        private LocalDate regDate;
    }
}
