package com.io661.extension.controller;

import com.io661.extension.IO661Extension;
import com.io661.extension.model.User.EditUserInfoReq;
import com.io661.extension.model.User.UserInfoRes;
import com.io661.extension.service.Impl.UserServiceImpl;
import com.io661.extension.service.UserService;
import com.io661.extension.util.User.UserCookieManager;
import javafx.application.Platform;
import javafx.event.ActionEvent;
import javafx.event.Event;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.geometry.Bounds;
import javafx.geometry.Point2D;
import javafx.geometry.Rectangle2D;
import javafx.scene.Node;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.Button;
import javafx.scene.control.Label;
import javafx.scene.control.TextField;
import javafx.scene.effect.ColorAdjust;
import javafx.scene.effect.GaussianBlur;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.input.MouseEvent;
import javafx.scene.layout.AnchorPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Pane;
import javafx.scene.paint.Color;
import javafx.stage.Modality;
import javafx.stage.Screen;
import javafx.stage.Stage;
import javafx.stage.StageStyle;
import javafx.stage.FileChooser;

import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.util.ResourceBundle;

public class UserController implements Initializable {
    // 用户信息侧边栏组件
    @FXML
    private ImageView userAvatar;
    @FXML
    private Label userNickname;
    @FXML
    private Label userBalance;
    @FXML
    private Button withdrawButton;
    @FXML
    private Button editProfileButton;
    @FXML
    private Button logoutButton;

    // 编辑用户信息对话框组件
    @FXML
    public Button editAvatar;
    @FXML
    public Button editNickName;
    @FXML
    public TextField nicknameField;
    @FXML
    public Button saveButton;
    @FXML
    public Button cancelButton;
    @FXML
    public ImageView profileImage;
    @FXML
    public Label nicknameLabel;
    @FXML
    public Label phoneLabel;
    @FXML
    public Label uidLabel;
    @FXML
    public Label realNameStatusLabel;
    @FXML
    public Button closeButton;
    @FXML
    public HBox nicknameEditBox;

    // 新增独立弹窗的组件
    @FXML
    private TextField nicknameInputField;
    @FXML
    private Button closeNicknameButton;
    @FXML
    private Button cancelNicknameButton;
    @FXML
    private Button submitNicknameButton;
    @FXML
    private ImageView avatarPreviewImage;
    @FXML
    private Button selectFileButton;
    @FXML
    private Button closeAvatarButton;
    @FXML
    private Button cancelAvatarButton;
    @FXML
    private Button submitAvatarButton;

    // 服务和状态变量
    private UserService userService;
    private Stage userInfoStage;
    private Stage editUserInfoStage;
    private Stage nicknameDialogStage;
    private Stage avatarDialogStage;
    private UserInfoRes.Data_ currentUserInfo;
    private String authToken;
    private File selectedAvatarFile;

    // 拖动窗口相关变量
    private double xOffset = 0;
    private double yOffset = 0;

    @Override
    public void initialize(URL url, ResourceBundle resourceBundle) {
        userService = new UserServiceImpl();
        authToken = LoginController.getAuthToken();
    }

    /**
     * 显示用户信息侧边栏
     * @param event 点击事件
     */
    public void showUserInfoCard(MouseEvent event) {
        try {
            // 如果侧边栏已经显示，则关闭它
            if (userInfoStage != null && userInfoStage.isShowing()) {
                userInfoStage.close();
                return;
            }

            // 获取用户信息
            refreshUserInfo();

            // 创建新的Stage
            userInfoStage = new Stage();
            userInfoStage.initStyle(StageStyle.TRANSPARENT);
            userInfoStage.initOwner(IO661Extension.getMainStage());

            // 获取事件源节点
            Node source = (Node) event.getSource();

            // 创建新的场景
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/com/io661/extension/fxml/components/user-info-card.fxml"));
            Parent userInfoPane = loader.load();

            // 获取控制器并填充用户信息
            UserController controller = loader.getController();
            if (controller != null && currentUserInfo != null) {
                controller.fillUserInfoCard(currentUserInfo);
            }

            Scene scene = new Scene(userInfoPane);
            scene.setFill(Color.TRANSPARENT);
            userInfoStage.setScene(scene);

            // 计算侧边栏位置
            Bounds sourceBounds = source.localToScreen(source.getBoundsInLocal());
            Bounds mainStageBounds = IO661Extension.getMainStage().getScene().getRoot().localToScreen(
                    IO661Extension.getMainStage().getScene().getRoot().getBoundsInLocal());

            // 计算合适的位置（优先显示在点击位置的右侧，如果空间不足则显示在左侧）
            double cardWidth = 250.0; // 侧边栏宽度
            double cardHeight = 300.0; // 侧边栏高度

            // 计算X坐标（确保在主窗口内）
            double xPos;
            if (sourceBounds.getMaxX() + cardWidth <= mainStageBounds.getMaxX()) {
                // 如果右侧有足够空间，显示在右侧
                xPos = sourceBounds.getMaxX() + 10;
            } else {
                // 否则显示在左侧
                xPos = sourceBounds.getMinX() - cardWidth - 10;
            }

            // 计算Y坐标（优先显示在点击位置上方）
            double yPos;
            if (sourceBounds.getMinY() - cardHeight >= mainStageBounds.getMinY()) {
                // 如果上方有足够空间，显示在上方
                yPos = sourceBounds.getMinY() - cardHeight;
            } else {
                // 否则显示在下方，但确保不超出主窗口底部
                yPos = Math.min(sourceBounds.getMinY(), mainStageBounds.getMaxY() - cardHeight);
            }

            userInfoStage.setX(xPos);
            userInfoStage.setY(yPos);

            // 确保卡片不会超出屏幕边界
            Rectangle2D screenBounds = Screen.getPrimary().getVisualBounds();
            if (userInfoStage.getX() < mainStageBounds.getMinX()) {
                userInfoStage.setX(mainStageBounds.getMinX());
            }
            if (userInfoStage.getY() < mainStageBounds.getMinY()) {
                userInfoStage.setY(mainStageBounds.getMinY());
            }

            // 设置事件处理
            setupUserInfoCardEvents(userInfoPane, controller);

            // 显示侧边栏
            userInfoStage.show();

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 设置用户信息侧边栏的事件处理
     * @param userInfoPane 用户信息侧边栏根节点
     * @param controller 侧边栏控制器实例
     */
    private void setupUserInfoCardEvents(Parent userInfoPane, UserController controller) {
        // 设置拖动功能
        userInfoPane.setOnMousePressed(event -> {
            xOffset = event.getSceneX();
            yOffset = event.getSceneY();
            event.consume(); // 阻止事件传播
        });

        userInfoPane.setOnMouseDragged(event -> {
            userInfoStage.setX(event.getScreenX() - xOffset);
            userInfoStage.setY(event.getScreenY() - yOffset);
            event.consume(); // 阻止事件传播
        });

        // 设置按钮事件（使用侧边栏控制器的按钮引用）
        if (controller != null) {
            System.out.println("设置侧边栏按钮事件");

            if (controller.withdrawButton != null) {
                System.out.println("设置提现按钮事件");
                controller.withdrawButton.setOnAction(this::handleWithdraw);
            } else {
                System.out.println("提现按钮为null");
            }

            if (controller.editProfileButton != null) {
                System.out.println("设置编辑资料按钮事件");
                controller.editProfileButton.setOnAction(this::handleEditProfile);
            } else {
                System.out.println("编辑资料按钮为null");
            }

            if (controller.logoutButton != null) {
                System.out.println("设置退出登录按钮事件");
                controller.logoutButton.setOnAction(this::handleLogout);
            } else {
                System.out.println("退出登录按钮为null");
            }
        } else {
            System.out.println("侧边栏控制器为null");
        }

        // 添加全局点击事件监听
        IO661Extension.getMainStage().getScene().setOnMousePressed(event -> {
            if (userInfoStage != null && userInfoStage.isShowing()) {
                // 将点击坐标转换为屏幕坐标
                Point2D screenPoint = new Point2D(event.getScreenX(), event.getScreenY());

                // 获取侧边栏窗口的边界
                if (userInfoStage.getScene() != null && userInfoStage.getScene().getRoot() != null) {
                    Bounds cardBounds = userInfoStage.getScene().getRoot().localToScreen(
                            userInfoStage.getScene().getRoot().getBoundsInLocal());

                    // 如果点击位置在侧边栏外部，则关闭侧边栏
                    if (!isPointInBounds(screenPoint, cardBounds)) {
                        userInfoStage.close();
                    }
                }
            }
        });
    }

    /**
     * 判断点击位置是否在指定边界内
     */
    private boolean isPointInBounds(Point2D point, Bounds bounds) {
        return point.getX() >= bounds.getMinX() && point.getX() <= bounds.getMaxX() &&
                point.getY() >= bounds.getMinY() && point.getY() <= bounds.getMaxY();
    }

    /**
     * 处理提现按钮点击事件
     * @param event 点击事件
     */
    private void handleWithdraw(ActionEvent event) {
        // 提现功能预留
        System.out.println("提现功能暂未实现");
    }

    /**
     * 处理编辑资料按钮点击事件
     * @param event 点击事件
     */
    private void handleEditProfile(ActionEvent event) {
        try {
            System.out.println("编辑资料按钮被点击");
            showEditUserInfoDialog();
        } catch (IOException e) {
            System.err.println("显示编辑用户信息对话框时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 显示编辑用户信息对话框
     */
    private void showEditUserInfoDialog() throws IOException {
        System.out.println("开始显示编辑用户信息对话框");

        // 关闭用户信息侧边栏
        if (userInfoStage != null && userInfoStage.isShowing()) {
            userInfoStage.close();
        }

        // 加载编辑用户信息对话框FXML
        System.out.println("加载FXML文件: /com/io661/extension/fxml/components/edit-user-info-card.fxml");
        FXMLLoader loader = new FXMLLoader(getClass().getResource("/com/io661/extension/fxml/components/edit-user-info-card.fxml"));
        Parent editUserInfoPane = loader.load();
        System.out.println("FXML文件加载成功");

        // 创建新的Stage
        editUserInfoStage = new Stage();
        editUserInfoStage.initStyle(StageStyle.TRANSPARENT);
        editUserInfoStage.initModality(Modality.APPLICATION_MODAL);
        editUserInfoStage.initOwner(IO661Extension.getMainStage());

        Scene scene = new Scene(editUserInfoPane);
        scene.setFill(Color.TRANSPARENT);
        editUserInfoStage.setScene(scene);

        // 设置背景模糊效果
        ColorAdjust colorAdjust = new ColorAdjust();
        colorAdjust.setBrightness(-0.2);
        GaussianBlur blur = new GaussianBlur(10);
        colorAdjust.setInput(blur);
        IO661Extension.getMainStage().getScene().getRoot().setEffect(colorAdjust);

        // 填充当前用户信息
        if (currentUserInfo != null) {
            UserController controller = loader.getController();
            if (controller != null) {
                // 填充头像
                if (controller.profileImage != null && currentUserInfo.getAvatar() != null && !currentUserInfo.getAvatar().isEmpty()) {
                    try {
                        Image avatar = new Image(currentUserInfo.getAvatar());
                        controller.profileImage.setImage(avatar);
                    } catch (Exception e) {
                        System.err.println("加载编辑对话框头像失败: " + e.getMessage());
                    }
                }

                // 填充昵称
                if (controller.nicknameLabel != null) {
                    controller.nicknameLabel.setText(currentUserInfo.getNickname());
                }

                // 填充UID
                if (controller.uidLabel != null) {
                    controller.uidLabel.setText(String.valueOf(currentUserInfo.getId()));
                }

                // 填充手机号
                if (controller.phoneLabel != null) {
                    controller.phoneLabel.setText(currentUserInfo.getPhone());
                }

                // 填充实名状态
                if (controller.realNameStatusLabel != null) {
                    String realNameStatus = (currentUserInfo.getName() != null && !currentUserInfo.getName().trim().isEmpty()) ? "已实名" : "未实名";
                    controller.realNameStatusLabel.setText(realNameStatus);
                }

                // 设置编辑字段的初始值
                if (controller.nicknameField != null) {
                    controller.nicknameField.setText(currentUserInfo.getNickname());
                }
            }
        }

        // 设置事件处理
        setupEditUserInfoDialogEvents(editUserInfoPane, loader.getController());

        // 显示对话框
        System.out.println("显示编辑用户信息对话框");
        editUserInfoStage.show();

        // 在显示后计算窗口位置使其居中显示
        Platform.runLater(() -> {
            double centerX = IO661Extension.getMainStage().getX() +
                    (IO661Extension.getMainStage().getWidth() - editUserInfoStage.getWidth()) / 2;
            double centerY = IO661Extension.getMainStage().getY() +
                    (IO661Extension.getMainStage().getHeight() - editUserInfoStage.getHeight()) / 2;
            editUserInfoStage.setX(centerX);
            editUserInfoStage.setY(centerY);
            System.out.println("编辑用户信息对话框已居中显示");
        });
    }

    /**
     * 设置编辑用户信息对话框的事件处理
     */
    private void setupEditUserInfoDialogEvents(Parent editUserInfoPane, UserController controller) {
        // 设置拖动功能
        editUserInfoPane.setOnMousePressed(event -> {
            xOffset = event.getSceneX();
            yOffset = event.getSceneY();
        });

        editUserInfoPane.setOnMouseDragged(event -> {
            editUserInfoStage.setX(event.getScreenX() - xOffset);
            editUserInfoStage.setY(event.getScreenY() - yOffset);
        });

        // 设置按钮事件（使用对话框控制器的按钮引用）
        if (controller != null) {
            // 设置编辑昵称按钮事件 - 直接显示独立的昵称修改弹窗
            if (controller.editNickName != null) {
                controller.editNickName.setOnAction(event -> {
                    // 显示独立的昵称修改弹窗
                    showNicknameEditDialog();
                });
            }

            // 设置编辑头像按钮事件
            if (controller.editAvatar != null) {
                controller.editAvatar.setOnAction(event -> {
                    // 显示独立的头像修改弹窗
                    showAvatarEditDialog();
                });
            }

            if (controller.saveButton != null) {
                controller.saveButton.setOnAction(event -> {
                    // 调用控制器的保存方法，传递当前主控制器实例
                    controller.handleSaveUserInfoInDialog(this);
                });
            }

            if (controller.cancelButton != null) {
                controller.cancelButton.setOnAction(event -> {
                    controller.switchToDisplayMode();
                });
            }

            if (controller.closeButton != null) {
                controller.closeButton.setOnAction(event -> {
                    editUserInfoStage.close();
                    // 移除背景模糊效果
                    IO661Extension.getMainStage().getScene().getRoot().setEffect(null);
                });
            }
        }

        // 设置关闭事件
        editUserInfoStage.setOnCloseRequest(event -> {
            // 移除背景模糊效果
            IO661Extension.getMainStage().getScene().getRoot().setEffect(null);
        });
    }

    /**
     * 处理保存用户信息按钮点击事件
     */
    @FXML
    public void handleSaveUserInfo(ActionEvent event) {
        if (nicknameField != null && nicknameField.isVisible()) {
            String newNickname = nicknameField.getText();

            // 创建请求对象
            EditUserInfoReq request = new EditUserInfoReq();
            request.setNickname(newNickname);

            // 调用服务更新用户信息
            boolean success = userService.editUserInfo(request, authToken);

            if (success) {
                // 更新当前用户信息
                if (currentUserInfo != null) {
                    currentUserInfo.setNickname(newNickname);
                }

                // 更新显示的昵称
                if (nicknameLabel != null) {
                    nicknameLabel.setText(newNickname);
                }

                // 切换回显示模式
                switchToDisplayMode();

                // 刷新用户信息侧边栏
                refreshUserInfo();

                System.out.println("用户信息更新成功");
            } else {
                System.err.println("用户信息更新失败");
            }
        }
    }

    /**
     * 在对话框中处理保存用户信息
     */
    public void handleSaveUserInfoInDialog(UserController mainController) {
        if (nicknameField != null && nicknameField.isVisible()) {
            String newNickname = nicknameField.getText().trim();

            if (newNickname.isEmpty()) {
                System.err.println("昵称不能为空");
                return;
            }

            // 创建请求对象
            EditUserInfoReq request = new EditUserInfoReq();
            request.setNickname(newNickname);

            // 获取认证令牌
            String token = mainController.authToken;
            if (token == null || token.isEmpty()) {
                token = LoginController.getAuthToken();
            }

            System.out.println("调用编辑用户信息API，昵称: " + newNickname);

            // 调用服务更新用户信息
            boolean success = mainController.userService.editUserInfo(request, token);

            if (success) {
                // 更新当前用户信息
                if (mainController.currentUserInfo != null) {
                    mainController.currentUserInfo.setNickname(newNickname);
                }

                // 更新显示的昵称
                if (nicknameLabel != null) {
                    nicknameLabel.setText(newNickname);
                }

                // 切换回显示模式
                switchToDisplayMode();

                // 刷新主控制器的用户信息
                mainController.refreshUserInfo();

                System.out.println("用户信息更新成功");
            } else {
                System.err.println("用户信息更新失败");
            }
        }
    }

    /**
     * 处理退出登录按钮点击事件
     * @param event 点击事件
     */
    private void handleLogout(ActionEvent event) {
        logout();
    }

    /**
     * 刷新用户信息
     */
    public void refreshUserInfo() {
        if (authToken == null || authToken.isEmpty()) {
            authToken = LoginController.getAuthToken();
        }

        if (authToken != null && !authToken.isEmpty()) {
            // 获取用户信息
            currentUserInfo = userService.getUserInfo(authToken);

            // 更新UI
            Platform.runLater(() -> {
                if (currentUserInfo != null) {
                    System.out.println("获取到用户信息: " + currentUserInfo.getNickname());
                    System.out.println("withdrawBalance: " + currentUserInfo.getWithdrawBalance());

                    // 更新用户头像
                    if (userAvatar != null && currentUserInfo.getAvatar() != null && !currentUserInfo.getAvatar().isEmpty()) {
                        try {
                            Image avatar = new Image(currentUserInfo.getAvatar());
                            userAvatar.setImage(avatar);
                        } catch (Exception e) {
                            System.err.println("加载用户头像失败: " + e.getMessage());
                        }
                    }

                    // 更新用户昵称
                    if (userNickname != null) {
                        userNickname.setText(currentUserInfo.getNickname());
                    }

                    // 更新钱包余额
                    if (userBalance != null) {
                        // 检查withdrawBalance是否为null
                        Integer withdrawBalance = currentUserInfo.getWithdrawBalance();
                        if (withdrawBalance != null) {
                            // 将分转换为元
                            double balanceInYuan = withdrawBalance / 100.0;
                            userBalance.setText(String.format("%.2f ¥", balanceInYuan));
                        } else {
                            System.err.println("withdrawBalance为null，使用balance字段");
                            // 如果withdrawBalance为null，尝试使用balance字段
                            Integer balance = currentUserInfo.getBalance();
                            if (balance != null) {
                                double balanceInYuan = balance / 100.0;
                                userBalance.setText(String.format("%.2f ¥", balanceInYuan));
                            } else {
                                userBalance.setText("0.00 ¥");
                            }
                        }
                    }
                }
            });
        }
    }

    /**
     * 退出登录
     */
    public void logout() {
        if (authToken == null || authToken.isEmpty()) {
            authToken = LoginController.getAuthToken();
        }

        if (authToken != null && !authToken.isEmpty()) {
            // 调用退出登录接口
            boolean success = userService.logout(authToken);

            if (success) {
                // 清除令牌
                LoginController.setAuthToken(null);
                UserCookieManager.deleteToken();

                // 关闭用户信息侧边栏
                if (userInfoStage != null && userInfoStage.isShowing()) {
                    userInfoStage.close();
                }

                // 显示登录窗口
                Platform.runLater(() -> {
                    try {
                        // 创建登录窗口
                        Stage loginStage = new Stage();
                        loginStage.initOwner(IO661Extension.getMainStage());
                        loginStage.initModality(Modality.APPLICATION_MODAL);
                        loginStage.initStyle(StageStyle.TRANSPARENT);
                        loginStage.setOnCloseRequest(Event::consume);

                        // 加载登录窗口
                        FXMLLoader loginLoader = new FXMLLoader(getClass().getResource("/com/io661/extension/fxml/login.fxml"));
                        Parent loginRoot = loginLoader.load();
                        Scene loginScene = new Scene(loginRoot);

                        // 设置登录窗口透明背景
                        loginScene.setFill(Color.TRANSPARENT);

                        // 设置登录窗口
                        loginStage.setTitle("IO661增值服务用户登录");
                        loginStage.setScene(loginScene);
                        loginStage.setResizable(false);

                        // 创建半透明遮罩效果
                        ColorAdjust colorAdjust = new ColorAdjust();
                        colorAdjust.setBrightness(-0.6); // 降低亮度，使主窗口变暗

                        // 给主窗口添加变暗效果
                        IO661Extension.getMainStage().getScene().getRoot().setEffect(colorAdjust);

                        // 显示登录窗口
                        loginStage.show();

                        // 在窗口显示后，调整位置使其居中
                        Platform.runLater(() -> {
                            double centerXPosition = IO661Extension.getMainStage().getX() + IO661Extension.getMainStage().getWidth()/2 - loginStage.getWidth()/2;
                            double centerYPosition = IO661Extension.getMainStage().getY() + IO661Extension.getMainStage().getHeight()/2 - loginStage.getHeight()/2;
                            loginStage.setX(centerXPosition);
                            loginStage.setY(centerYPosition);
                        });

                        // 当登录窗口关闭时，移除主窗口的变暗效果
                        loginStage.setOnHidden(event -> IO661Extension.getMainStage().getScene().getRoot().setEffect(null));
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                });
            }
        }
    }

    /**
     * 渲染用户信息
     */
    public void loadUserInfo() throws IOException {
        refreshUserInfo();
    }



    /**
     * 取消编辑按钮点击事件
     */
    @FXML
    public void handleCancelEdit(ActionEvent actionEvent) {
        // 切换回显示模式
        switchToDisplayMode();
    }

    /**
     * 切换到编辑模式
     */
    private void switchToEditMode() {
        // 隐藏昵称显示区域
        if (nicknameLabel != null && nicknameLabel.getParent() != null) {
            nicknameLabel.getParent().setVisible(false);
            nicknameLabel.getParent().setManaged(false);
        }

        // 显示昵称编辑区域
        if (nicknameEditBox != null) {
            nicknameEditBox.setVisible(true);
            nicknameEditBox.setManaged(true);
        }

        // 设置焦点到编辑字段
        if (nicknameField != null) {
            nicknameField.requestFocus();
        }
    }

    /**
     * 切换到显示模式
     */
    private void switchToDisplayMode() {
        // 显示昵称显示区域
        if (nicknameLabel != null && nicknameLabel.getParent() != null) {
            nicknameLabel.getParent().setVisible(true);
            nicknameLabel.getParent().setManaged(true);
        }

        // 隐藏昵称编辑区域
        if (nicknameEditBox != null) {
            nicknameEditBox.setVisible(false);
            nicknameEditBox.setManaged(false);
        }

        // 恢复原始值
        if (currentUserInfo != null && nicknameField != null) {
            nicknameField.setText(currentUserInfo.getNickname());
        }
    }

    /**
     * 关闭编辑对话框
     */
    @FXML
    public void handleCloseDialog(ActionEvent actionEvent) {
        if (editUserInfoStage != null && editUserInfoStage.isShowing()) {
            editUserInfoStage.close();
            // 移除背景模糊效果
            IO661Extension.getMainStage().getScene().getRoot().setEffect(null);
        }
    }

    /**
     * 填充用户信息卡片数据
     */
    public void fillUserInfoCard(UserInfoRes.Data_ userInfo) {
        if (userInfo == null) return;

        Platform.runLater(() -> {
            // 更新用户头像
            if (userAvatar != null && userInfo.getAvatar() != null && !userInfo.getAvatar().isEmpty()) {
                try {
                    Image avatar = new Image(userInfo.getAvatar());
                    userAvatar.setImage(avatar);
                } catch (Exception e) {
                    System.err.println("加载用户头像失败: " + e.getMessage());
                }
            }

            // 更新用户昵称
            if (userNickname != null) {
                userNickname.setText(userInfo.getNickname());
            }

            // 更新钱包余额
            if (userBalance != null) {
                // 检查withdrawBalance是否为null
                Integer withdrawBalance = userInfo.getWithdrawBalance();
                if (withdrawBalance != null) {
                    // 将分转换为元
                    double balanceInYuan = withdrawBalance / 100.0;
                    userBalance.setText(String.format("%.2f ¥", balanceInYuan));
                } else {
                    // 如果withdrawBalance为null，尝试使用balance字段
                    Integer balance = userInfo.getBalance();
                    if (balance != null) {
                        double balanceInYuan = balance / 100.0;
                        userBalance.setText(String.format("%.2f ¥", balanceInYuan));
                    } else {
                        userBalance.setText("0.00 ¥");
                    }
                }
            }
        });
    }

    /**
     * 显示修改昵称弹窗
     */
    private void showNicknameEditDialog() {
        try {
            // 关闭编辑用户信息对话框
            if (editUserInfoStage != null && editUserInfoStage.isShowing()) {
                editUserInfoStage.close();
                // 移除背景模糊效果
                IO661Extension.getMainStage().getScene().getRoot().setEffect(null);
            }

            // 创建新的Stage
            nicknameDialogStage = new Stage();
            nicknameDialogStage.initStyle(StageStyle.TRANSPARENT);
            nicknameDialogStage.initModality(Modality.APPLICATION_MODAL);
            nicknameDialogStage.initOwner(IO661Extension.getMainStage());

            // 加载FXML
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/com/io661/extension/fxml/components/edit-nickname-dialog.fxml"));
            Parent nicknameDialogPane = loader.load();

            Scene scene = new Scene(nicknameDialogPane);
            scene.setFill(Color.TRANSPARENT);
            nicknameDialogStage.setScene(scene);

            // 设置背景模糊效果
            ColorAdjust colorAdjust = new ColorAdjust();
            colorAdjust.setBrightness(-0.2);
            GaussianBlur blur = new GaussianBlur(10);
            colorAdjust.setInput(blur);
            IO661Extension.getMainStage().getScene().getRoot().setEffect(colorAdjust);

            // 获取控制器并设置初始值
            UserController controller = loader.getController();
            if (controller != null && currentUserInfo != null) {
                if (controller.nicknameInputField != null) {
                    controller.nicknameInputField.setText(currentUserInfo.getNickname());
                }
            }

            // 设置事件处理
            setupNicknameDialogEvents(nicknameDialogPane, controller);

            // 显示对话框
            nicknameDialogStage.show();

            // 居中显示
            Platform.runLater(() -> {
                double centerX = IO661Extension.getMainStage().getX() +
                        (IO661Extension.getMainStage().getWidth() - nicknameDialogStage.getWidth()) / 2;
                double centerY = IO661Extension.getMainStage().getY() +
                        (IO661Extension.getMainStage().getHeight() - nicknameDialogStage.getHeight()) / 2;
                nicknameDialogStage.setX(centerX);
                nicknameDialogStage.setY(centerY);
            });

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 显示修改头像弹窗
     */
    private void showAvatarEditDialog() {
        try {
            // 关闭编辑用户信息对话框
            if (editUserInfoStage != null && editUserInfoStage.isShowing()) {
                editUserInfoStage.close();
                // 移除背景模糊效果
                IO661Extension.getMainStage().getScene().getRoot().setEffect(null);
            }

            // 创建新的Stage
            avatarDialogStage = new Stage();
            avatarDialogStage.initStyle(StageStyle.TRANSPARENT);
            avatarDialogStage.initModality(Modality.APPLICATION_MODAL);
            avatarDialogStage.initOwner(IO661Extension.getMainStage());

            // 加载FXML
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/com/io661/extension/fxml/components/edit-avatar-dialog.fxml"));
            Parent avatarDialogPane = loader.load();

            Scene scene = new Scene(avatarDialogPane);
            scene.setFill(Color.TRANSPARENT);
            avatarDialogStage.setScene(scene);

            // 设置背景模糊效果
            ColorAdjust colorAdjust = new ColorAdjust();
            colorAdjust.setBrightness(-0.2);
            GaussianBlur blur = new GaussianBlur(10);
            colorAdjust.setInput(blur);
            IO661Extension.getMainStage().getScene().getRoot().setEffect(colorAdjust);

            // 获取控制器并设置初始头像
            UserController controller = loader.getController();
            if (controller != null && currentUserInfo != null) {
                if (controller.avatarPreviewImage != null && currentUserInfo.getAvatar() != null && !currentUserInfo.getAvatar().isEmpty()) {
                    try {
                        Image avatar = new Image(currentUserInfo.getAvatar());
                        controller.avatarPreviewImage.setImage(avatar);
                    } catch (Exception e) {
                        System.err.println("加载头像预览失败: " + e.getMessage());
                    }
                }
            }

            // 设置事件处理
            setupAvatarDialogEvents(avatarDialogPane, controller);

            // 显示对话框
            avatarDialogStage.show();

            // 居中显示
            Platform.runLater(() -> {
                double centerX = IO661Extension.getMainStage().getX() +
                        (IO661Extension.getMainStage().getWidth() - avatarDialogStage.getWidth()) / 2;
                double centerY = IO661Extension.getMainStage().getY() +
                        (IO661Extension.getMainStage().getHeight() - avatarDialogStage.getHeight()) / 2;
                avatarDialogStage.setX(centerX);
                avatarDialogStage.setY(centerY);
            });

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 设置昵称编辑弹窗的事件处理
     */
    private void setupNicknameDialogEvents(Parent nicknameDialogPane, UserController controller) {
        // 设置拖动功能
        nicknameDialogPane.setOnMousePressed(event -> {
            xOffset = event.getSceneX();
            yOffset = event.getSceneY();
        });

        nicknameDialogPane.setOnMouseDragged(event -> {
            nicknameDialogStage.setX(event.getScreenX() - xOffset);
            nicknameDialogStage.setY(event.getScreenY() - yOffset);
        });

        // 设置按钮事件
        if (controller != null) {
            if (controller.closeNicknameButton != null) {
                controller.closeNicknameButton.setOnAction(event -> {
                    nicknameDialogStage.close();
                    IO661Extension.getMainStage().getScene().getRoot().setEffect(null);
                });
            }

            if (controller.cancelNicknameButton != null) {
                controller.cancelNicknameButton.setOnAction(event -> {
                    nicknameDialogStage.close();
                    IO661Extension.getMainStage().getScene().getRoot().setEffect(null);
                });
            }

            if (controller.submitNicknameButton != null) {
                controller.submitNicknameButton.setOnAction(event -> {
                    controller.handleSubmitNickname(this);
                });
            }
        }

        // 设置关闭事件
        nicknameDialogStage.setOnCloseRequest(event -> {
            IO661Extension.getMainStage().getScene().getRoot().setEffect(null);
        });
    }

    /**
     * 设置头像编辑弹窗的事件处理
     */
    private void setupAvatarDialogEvents(Parent avatarDialogPane, UserController controller) {
        // 设置拖动功能
        avatarDialogPane.setOnMousePressed(event -> {
            xOffset = event.getSceneX();
            yOffset = event.getSceneY();
        });

        avatarDialogPane.setOnMouseDragged(event -> {
            avatarDialogStage.setX(event.getScreenX() - xOffset);
            avatarDialogStage.setY(event.getScreenY() - yOffset);
        });

        // 设置按钮事件
        if (controller != null) {
            if (controller.closeAvatarButton != null) {
                controller.closeAvatarButton.setOnAction(event -> {
                    avatarDialogStage.close();
                    IO661Extension.getMainStage().getScene().getRoot().setEffect(null);
                });
            }

            if (controller.cancelAvatarButton != null) {
                controller.cancelAvatarButton.setOnAction(event -> {
                    avatarDialogStage.close();
                    IO661Extension.getMainStage().getScene().getRoot().setEffect(null);
                });
            }

            if (controller.selectFileButton != null) {
                controller.selectFileButton.setOnAction(event -> {
                    controller.handleSelectAvatarFile(this);
                });
            }

            if (controller.submitAvatarButton != null) {
                controller.submitAvatarButton.setOnAction(event -> {
                    controller.handleSubmitAvatar(this);
                });
            }
        }

        // 设置关闭事件
        avatarDialogStage.setOnCloseRequest(event -> {
            IO661Extension.getMainStage().getScene().getRoot().setEffect(null);
        });
    }

    /**
     * 处理提交昵称
     */
    public void handleSubmitNickname(UserController mainController) {
        if (nicknameInputField != null) {
            String newNickname = nicknameInputField.getText().trim();

            if (newNickname.isEmpty()) {
                System.err.println("昵称不能为空");
                return;
            }

            // 创建请求对象
            EditUserInfoReq request = new EditUserInfoReq();
            request.setNickname(newNickname);

            // 获取认证令牌
            String token = mainController.authToken;
            if (token == null || token.isEmpty()) {
                token = LoginController.getAuthToken();
            }

            System.out.println("调用编辑用户信息API，昵称: " + newNickname);

            // 调用服务更新用户信息
            boolean success = mainController.userService.editUserInfo(request, token);

            if (success) {
                // 更新当前用户信息
                if (mainController.currentUserInfo != null) {
                    mainController.currentUserInfo.setNickname(newNickname);
                }

                // 刷新主控制器的用户信息
                mainController.refreshUserInfo();

                // 关闭弹窗 - 使用主控制器的 nicknameDialogStage
                if (mainController.nicknameDialogStage != null) {
                    mainController.nicknameDialogStage.close();
                }
                IO661Extension.getMainStage().getScene().getRoot().setEffect(null);

                System.out.println("用户昵称更新成功");
            } else {
                System.err.println("用户昵称更新失败");
            }
        }
    }

    /**
     * 处理选择头像文件
     */
    public void handleSelectAvatarFile(UserController mainController) {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("选择头像文件");
        fileChooser.getExtensionFilters().addAll(
                new FileChooser.ExtensionFilter("图片文件", "*.png", "*.jpg", "*.jpeg", "*.gif", "*.bmp")
        );

        File selectedFile = fileChooser.showOpenDialog(avatarDialogStage);
        if (selectedFile != null) {
            selectedAvatarFile = selectedFile;

            // 显示预览
            if (avatarPreviewImage != null) {
                try {
                    Image previewImage = new Image(selectedFile.toURI().toString());
                    avatarPreviewImage.setImage(previewImage);
                } catch (Exception e) {
                    System.err.println("加载头像预览失败: " + e.getMessage());
                }
            }
        }
    }

    /**
     * 处理提交头像
     */
    public void handleSubmitAvatar(UserController mainController) {
        if (selectedAvatarFile == null) {
            System.err.println("请先选择头像文件");
            return;
        }

        // 创建请求对象
        EditUserInfoReq request = new EditUserInfoReq();
        // 这里需要根据实际API设置头像，可能需要先上传文件获取URL
        // request.setAvatar(avatarUrl);

        // 获取认证令牌
        String token = mainController.authToken;
        if (token == null || token.isEmpty()) {
            token = LoginController.getAuthToken();
        }

        System.out.println("调用编辑用户信息API，头像文件: " + selectedAvatarFile.getName());

        // 调用服务更新用户信息
        boolean success = mainController.userService.editUserInfo(request, token);

        if (success) {
            // 刷新主控制器的用户信息
            mainController.refreshUserInfo();

            // 关闭弹窗 - 使用主控制器的 avatarDialogStage
            if (mainController.avatarDialogStage != null) {
                mainController.avatarDialogStage.close();
            }
            IO661Extension.getMainStage().getScene().getRoot().setEffect(null);

            System.out.println("用户头像更新成功");
        } else {
            System.err.println("用户头像更新失败");
        }
    }

    // FXML绑定的方法
    @FXML
    public void handleCloseNicknameDialog() {
        if (nicknameDialogStage != null) {
            nicknameDialogStage.close();
            IO661Extension.getMainStage().getScene().getRoot().setEffect(null);
        }
    }

    @FXML
    public void handleCancelNicknameEdit() {
        if (nicknameDialogStage != null) {
            nicknameDialogStage.close();
            IO661Extension.getMainStage().getScene().getRoot().setEffect(null);
        }
    }

    @FXML
    public void handleCloseAvatarDialog() {
        if (avatarDialogStage != null) {
            avatarDialogStage.close();
            IO661Extension.getMainStage().getScene().getRoot().setEffect(null);
        }
    }

    @FXML
    public void handleCancelAvatarEdit() {
        if (avatarDialogStage != null) {
            avatarDialogStage.close();
            IO661Extension.getMainStage().getScene().getRoot().setEffect(null);
        }
    }
}