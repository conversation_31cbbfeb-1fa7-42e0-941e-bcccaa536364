 /* 主标题样式 */
.title-label {
    -fx-font-size: 24px;
    -fx-font-weight: bold;
    -fx-text-fill: #333333;
}

/* 部分标题样式 */
.section-title {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: #444444;
    -fx-padding: 5 0 5 0;
}

/* 描述文本样式 */
.description-label {
    -fx-font-size: 14px;
    -fx-text-fill: #666666;
}

/* 帮助说明文本样式 */
.help-label {
    -fx-font-size: 13px;
    -fx-text-fill: #777777;
}

/* 主要按钮样式 */
.primary-button {
    -fx-background-color: #4CAF50;
    -fx-text-fill: white;
    -fx-font-size: 14px;
    -fx-padding: 8 15 8 15;
    -fx-background-radius: 4;
}

.primary-button:hover {
    -fx-background-color: #45a049;
}

.primary-button:pressed {
    -fx-background-color: #3d8b40;
}

/* 次要按钮样式 */
.secondary-button {
    -fx-background-color: #f0f0f0;
    -fx-text-fill: #333333;
    -fx-font-size: 14px;
    -fx-padding: 8 15 8 15;
    -fx-background-radius: 4;
    -fx-border-color: #cccccc;
    -fx-border-radius: 4;
}

.secondary-button:hover {
    -fx-background-color: #e8e8e8;
}

.secondary-button:pressed {
    -fx-background-color: #d0d0d0;
}

/* 结果标签样式 */
.result-label {
    -fx-font-size: 16px;
    -fx-font-weight: bold;
    -fx-text-fill: #2196F3;
}

/* 文本输入框样式 */
.text-field {
    -fx-padding: 5 10 5 10;
    -fx-background-radius: 4;
    -fx-border-radius: 4;
    -fx-border-color: #cccccc;
    -fx-background-color: white;
}

.text-field:focused {
    -fx-border-color: #4CAF50;
}

/* 下拉框样式 */
.combo-box {
    -fx-background-color: white;
    -fx-border-color: #cccccc;
    -fx-border-radius: 4;
    -fx-background-radius: 4;
}

.combo-box:focused {
    -fx-border-color: #4CAF50;
}

/* 分隔线样式 */
.separator {
    -fx-padding: 10 0 10 0;
}

/* 滚动面板样式 */
.scroll-pane {
    -fx-background-color: white;
    -fx-padding: 0;
}

.scroll-pane > .viewport {
    -fx-background-color: white;
}

/* 网格布局样式 */
.grid-pane {
    -fx-hgap: 10;
    -fx-vgap: 10;
}